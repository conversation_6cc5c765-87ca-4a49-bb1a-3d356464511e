﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<AzureFunctionsVersion>v4</AzureFunctionsVersion>
		<DockerFastModeProjectMountDirectory>/home/<USER>/wwwroot</DockerFastModeProjectMountDirectory>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<ItemGroup>
		<None Remove="appsettings.dev.json" />
		<None Remove="appsettings.json" />
		<None Remove="appsettings.prd.json" />
		<None Remove="appsettings.qa.json" />
	</ItemGroup>
	<ItemGroup>
		<Content Include="appsettings.dev.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="appsettings.prd.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="appsettings.qa.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.Azure.Cosmos" Version="3.37.0" />
		<PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.4.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="6.0.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
		<PackageReference Include="Microsoft.Extensions.DependencyModel" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.0" />
		<FunctionsPreservedDependencies Include="Microsoft.Extensions.DependencyModel.dll" />
	</ItemGroup>
	<ItemGroup>
		<None Update="host.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="local.settings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</None>
	</ItemGroup>
</Project>
