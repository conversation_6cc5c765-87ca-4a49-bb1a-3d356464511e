﻿using Lrb.Application.Notifications.Dtos;
using System;

namespace LeadRotationExecutor.Dtos
{
    public class LeadRotationDto
    {
        public Guid LeadId { get; set; }
        public LrbTenantInfoDto? TenantInfoDto { get; set; }
        public Guid CurrentUserId { get; set; }
        public TimeSpan? ShiftStartTime { get; set; }
        public TimeSpan? ShiftEndTime { get; set; }
        public Guid? AssignedUser { get; set; }
        public bool? ShouldSendPreAlertNotification { get; set; } = true;
        public bool? ShouldSendPostAlertNotification { get; set; }
        public double? BufferTime { get; set; }

    }
}
