﻿{
  "SecuritySettings": {
    "Provider": "Jwt",
    "RequireConfirmedAccount": true,
    "JwtSettings": {
      "key": "S0M3RAN0MS3CR3T!1!MAG1C!1!",
      "tokenExpirationInMinutes": 60,
      "refreshTokenExpirationInDays": 7
    },
    "AzureAd": {
      "Instance": "https://login.microsoftonline.com/",
      "Domain": "<Your Domain>.onmicrosoft.com",
      "TenantId": "organizations",
      "ClientId": "<Your ClientId of the AzureAd Server App Registration>",
      "Scopes": "access_as_user",
      "RootIssuer": "https://sts.windows.net/<Your AzureAd TenantId>/"
    },
    "CognitoJwtSettings": {
      "Region": "ap-south-1",
      "UserPoolId": "ap-south-1_M1yEQj08x",
      "WebAppClientId": "ftbimj4s56gp3j4s4q4us4m7j",
      "WebAppClientSecret": "1q91qkgvkgo3limuk0n98tssifp1hnpfpf8b97pfpujn43kvas3e",
      "MobileAppClientId": "35ar5t19r7u5vb7b2c9kkpfee8",
      "MicrositeClientId": "",
      "AccessKeyId": "********************",
      "AccessSecretKey": "TnH2dknft7vO0FrXvcvLFe7C8kgDJ8Oa3qIUBu0r",
      "JsonWebKeySet": {
        "keys": [
          {
            "alg": "RS256",
            "e": "AQAB",
            "kid": "YdrKXk4NkWpu5JkX6F3cCbA+G/CQbUiZdG5iGBhIY+I=",
            "kty": "RSA",
            "n": "3ar-s20xJjuFbYBdqhZrJisyFG-pFY1ZTIqqzmlbOsaiLQrUCQMIMeiujgCe_blqc4z-baix14gtn08nVV-bbqmuMfHQvRrOClFfBVAlOVZtOonGoNwiDokwcApmAQJMBcyGdM6oRlW0txJ7NuQI7t4qlN46l9Ehyc5acMvP241Z1waDPdsTpFCSzYp9jDzZyNzKytpEOOV8qKgmV2Lw01qXfbxH7_rtbL_GZlNnt3LVNLqJzUDRXP0jjG5UJv4en46xy40KULSWN5qa9aawVeLNVNEIIWrEP122KPP50pyJARm4Vd8A_31H8IUzQ_q5DrWOh24t8G-jxZDrGWddwQ",
            "use": "sig"
          },
          {
            "alg": "RS256",
            "e": "AQAB",
            "kid": "D9VKWk5GA99KU+d5GeW31s5brvkeSmNGx95dTkqNRj8=",
            "kty": "RSA",
            "n": "w5itZIRXygJScDKWO99Jnt1G42HeOcq_Np1MZ3_hpydk819QmjN5zIjmtuWQc_t7O1PRuJnqaFY-wkYHQ7BDGZJ3WoZ6c7ObTgruo_kp3NJmw1FEzcx8Jf10d5mfmG5dFW9J6kqnjtRd_akGc9v_2E-piLwYUDe1vPI6Hay56Akw8joCFE_g3RlxEgx8RD1LfkUuvXBE-TuAibSkQLQTRmuaFEH9vcuPrB2cWY7wBk47A8gk0GJNINRd7ACB_2pvW6pAqVLr2IdwcPj-St2awUAxt_djMMH1Yfy2e2mZMqCIsbicG8lbBN3h6KkE1IwJ6Fko3Ef-i3KEYBjQ0qc-zw",
            "use": "sig"
          }
        ]
      }
    },
    "Swagger": {
      "AuthorizationUrl": "https://login.microsoftonline.com/organizations/oauth2/v2.0/authorize",
      "TokenUrl": "https://login.microsoftonline.com/organizations/oauth2/v2.0/token",
      "ApiScope": "api://<Your ClientId of the AzureAd Server App Registration>/access_as_user",
      "OpenIdClientId": "<Your ClientId of the AzureAd Client App Registration>"
    }
  }
}