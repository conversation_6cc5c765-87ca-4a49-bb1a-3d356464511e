#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.
#
#FROM mcr.microsoft.com/dotnet/runtime:6.0 AS base
#WORKDIR /app
#
#ENV ASPNETCORE_ENVIRONMENT="dev"
#
#FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
#WORKDIR /src
#COPY ["ExcelUpload/ExcelUpload.csproj", "ExcelUpload/"]
#RUN dotnet restore "ExcelUpload/ExcelUpload.csproj"
#COPY . .
#WORKDIR "/src/ExcelUpload"
#RUN dotnet build "ExcelUpload.csproj" -c Release -o /app/build
#
#FROM build AS publish
#RUN dotnet publish "ExcelUpload.csproj" -c Release -o /app/publish
#
#FROM base AS final
#WORKDIR /app
#COPY --from=publish /app/publish .
#ENTRYPOINT ["dotnet", "ExcelUpload.dll"]
#CMD ["inputParameter"]

FROM mcr.microsoft.com/dotnet/runtime:6.0 AS base
WORKDIR /app

ENV ASPNETCORE_ENVIRONMENT="qa" 

FROM mcr.microsoft.com/dotnet/sdk:6.0-bullseye-slim AS build
WORKDIR /src
COPY ["ExcelUpload/ExcelUpload.csproj", "ExcelUpload/"]
RUN dotnet restore "ExcelUpload/ExcelUpload.csproj"
COPY . .
WORKDIR "/src/ExcelUpload"
RUN dotnet build "ExcelUpload.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ExcelUpload.csproj" -c Release -o /app/publish

FROM mcr.microsoft.com/dotnet/sdk:6.0-bullseye-slim AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ExcelUpload.dll"]
CMD ["inputParameter"]
