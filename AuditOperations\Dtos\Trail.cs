﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AuditOperation
{
    public class Trail : BaseEntity, ICosmoItem
    {
        public Guid UserId { get; set; }
        public string? Type { get; set; }
        public string? TableName { get; set; }
        public DateTime DateTime { get; set; }
        public string? OldValues { get; set; }
        public string? NewValues { get; set; }
        public string? AffectedColumns { get; set; }
        public string? PrimaryKey { get; set; }
        public string PartitionId { get => Id.ToString(); set => Id.ToString(); }
        public string PartitionKey { get; set; } = "Lrb-AuditTrails";
    }
    public class AuditTrails : ICosmoItem
    {
        public string DateTimeRange { get; set; } = default!;
        public List<Trail>? Trails { get; set; }
        public string PartitionId { get; set; } = Guid.NewGuid().ToString();
        public string PartitionKey { get; set; } = "Lrb-AuditTrails";
    }

    public class BaseEntity
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public bool IsDeleted { get; set; }
        public string TenantId { get; set; } = default!;
    }
    public interface ICosmoItem
    {
        [JsonProperty(PropertyName = "id")]
        string PartitionId { get; set; }

        [JsonProperty(PropertyName = "partitionKey")]
        string PartitionKey { get; set; }
    }
}
