﻿{
  "SecuritySettings": {
    "Provider": "Jwt",
    "RequireConfirmedAccount": true,
    "JwtSettings": {
      "key": "S0M3RAN0MS3CR3T!1!MAG1C!1!",
      "tokenExpirationInMinutes": 60,
      "refreshTokenExpirationInDays": 7
    },
    "AzureAd": {
      "Instance": "https://login.microsoftonline.com/",
      "Domain": "<Your Domain>.onmicrosoft.com",
      "TenantId": "organizations",
      "ClientId": "<Your ClientId of the AzureAd Server App Registration>",
      "Scopes": "access_as_user",
      "RootIssuer": "https://sts.windows.net/<Your AzureAd TenantId>/"
    },
    "CognitoJwtSettings": {
      "Region": "ap-south-1",
      "UserPoolId": "ap-south-1_Mg4tzL226",
      "WebAppClientId": "3acjel01anetjj6ek8of4u4ag2",
      "WebAppClientSecret": "1q91qkgvkgo3limuk0n98tssifp1hnpfpf8b97pfpujn43kvas3e",
      "MobileAppClientId": "113avsibconbtv6eh16oth9hu8",
      "MicrositeClientId": "",
      "AccessKeyId": "********************",
      "AccessSecretKey": "TnH2dknft7vO0FrXvcvLFe7C8kgDJ8Oa3qIUBu0r",
      "JsonWebKeySet": {
        "keys": [
          {
            "alg": "RS256",
            "e": "AQAB",
            "kid": "o6zBEIVRIV6ibNUjOfWqwk9dzr2o7u1DsLVEFW0rEnY=",
            "kty": "RSA",
            "n": "oj7TfHG5Id1WnHGVgk8sfPurzQ25zBwxvZ7fqKpGrekopluXUMbaW-7xUP0upm_E7_Wo3Sj_4iUKyc0ncCdDzwbnnIMN-Uh_AH6N9y192lGl9DCBwgvw1kGD-oJdOPSjIB_-L77d-NQfc8Ugzh7EPfCnBdGwUs_xmblYUOBwEC6t6PBoA_auLSzq5VPayV7NaGYPyfIFyq7YxQ7XCb8WbKIbljrNz93GLQ6J2ESKqnwoDeCYtNyDyYOS0SP8eQ-FsogacKbNCo2644OyTF6OX3VMxz7NzDTPf9RIw9faO901GLnlL4VeyVZIiv3kV8ho0chJQcjznmKuaEyROxnscQ",
            "use": "sig"
          },
          {
            "alg": "RS256",
            "e": "AQAB",
            "kid": "c7my7Y2YQpEJvy0oYzyuU9uaREl6Szem+HavGsuA1VM=",
            "kty": "RSA",
            "n": "9PhBTDfH3WcsuHDj3ZvG8PG-4CfAfZSapt38LLxia4FG30lDaWwQE3oMgO4M12ZY_cAEe2OHvleUBkbQuX9MgPUtxCGCMZI5J-EQHRqF7ps_yWx5INnXomeLqC3veKibc5xUWSVdvlRPNtQD5LYR0gNvqXY3GlhBvL09TzfCs2CgqkBzPAe748mlOxvNrKMDfH1ufp21Mtj3Tugk273U95Jbu0lToHaCwUyQy8ZvhJt2hcn93wzwXvIiidkHLNu8uZ7muV9-v_sS9qC-icYt4m26PunvYpRNyCGxaF5-Fg_S-caRcGnYAIa79Wytb0FiJXTJbY56jwvZfUAr9keTdQ",
            "use": "sig"
          }
        ]
      }
    },
    "Swagger": {
      "AuthorizationUrl": "https://login.microsoftonline.com/organizations/oauth2/v2.0/authorize",
      "TokenUrl": "https://login.microsoftonline.com/organizations/oauth2/v2.0/token",
      "ApiScope": "api://<Your ClientId of the AzureAd Server App Registration>/access_as_user",
      "OpenIdClientId": "<Your ClientId of the AzureAd Client App Registration>"
    }
  }
}