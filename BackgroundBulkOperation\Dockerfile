#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/azure-functions/dotnet:4 AS base
WORKDIR /home/<USER>/wwwroot
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

ENV ASPNETCORE_ENVIRONMENT="qa" 

COPY ["BackgroundBulkOperation/BackgroundBulkOperation.csproj", "BackgroundBulkOperation/"]
RUN dotnet restore "./BackgroundBulkOperation/BackgroundBulkOperation.csproj"
COPY . .
WORKDIR "/src/BackgroundBulkOperation"
RUN dotnet build "./BackgroundBulkOperation.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./BackgroundBulkOperation.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /home/<USER>/wwwroot
COPY --from=publish /app/publish .
ENV AzureWebJobsScriptRoot=/home/<USER>/wwwroot \
    AzureFunctionsJobHost__Logging__Console__IsEnabled=true