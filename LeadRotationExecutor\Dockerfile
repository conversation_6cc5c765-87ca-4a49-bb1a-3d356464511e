#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/azure-functions/dotnet:4 AS base
WORKDIR /home/<USER>/wwwroot
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src

ENV ASPNETCORE_ENVIRONMENT="qa"

COPY ["LeadRotationExecutor/LeadRotationExecutor.csproj", "LeadRotationExecutor/"]
RUN dotnet restore "LeadRotationExecutor/LeadRotationExecutor.csproj"
COPY . .
WORKDIR "/src/LeadRotationExecutor"
RUN dotnet build "LeadRotationExecutor.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "LeadRotationExecutor.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /home/<USER>/wwwroot
COPY --from=publish /app/publish .
ENV AzureWebJobsScriptRoot=/home/<USER>/wwwroot \
    AzureFunctionsJobHost__Logging__Console__IsEnabled=true