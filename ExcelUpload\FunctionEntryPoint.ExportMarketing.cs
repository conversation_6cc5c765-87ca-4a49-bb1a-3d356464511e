﻿using Lrb.Application;
using Lrb.Application.Agency.Web;
using Lrb.Application.Agency.Web.Requests;
using Lrb.Application.Campaigns.Request;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application.ChannelPartner.Web.Dtos;
using Lrb.Application.ChannelPartner.Web.Request;
using Lrb.Application.ChannelPartner.Web.Specs;
using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Marketing.Web.Dtos;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Marketing;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Shared.Extensions;
using Mapster;
using Newtonsoft.Json;

namespace ExcelUpload
{
    partial class FunctionEntryPoint
    {
        public async Task ExportMarketingAgencyHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportMarketingTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
            var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
            var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ExportLead), CancellationToken.None)).FirstOrDefault();
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            List<Lrb.Application.Identity.Users.UserDetailsDto> users = new(await _userService.GetListAsync(cancellationToken));
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);

            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };

            try
            {
                if (tracker != null && serviceProvider != null && exportEmailTemplate != null)
                {
                    RunAWSBatchForMarketingAgencyRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForMarketingAgencyRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        List<string> agencyNamesWithFilters;
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var agenciesCount = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<ViewAgencyDto>("LeadratBlack", "Agencies_Affiliated_Count", new
                        {
                            tenant_id = tenantId,
                            agencies = request.AgencyNames?.Select(name => name.Replace(" ", "").ToLower()).ToList(),
                            leads_min = request.LeadsMinCount,
                            leads_max = request.LeadsMaxCount,
                            prospects_min = request.ProspectMinCount,
                            prospects_max = request.ProspectMaxCount
                        }))?.ToList() ?? new List<ViewAgencyDto>();
                        var agencies = await _agencyRepo.ListAsync(new ExportAgenciesByFiltersSpec(request, agenciesCount.Select(a => a.Id).ToList()), cancellationToken);
                        request.PageSize = int.MaxValue;
                        var totalCount = await _agencyRepo.CountAsync(new ExportAgenciesByFiltersSpec(request, agenciesCount.Select(a => a.Id).ToList()), cancellationToken);
                        var agenciesWithCount = agenciesCount
                        .Where(agency => agencies.Any(ea => ea.Id == agency.Id))
                        .Select(agency =>
                        {
                            var originalLeadsCount = agency.LeadsCount;
                            var originalProspectCount = agency.ProspectCount;
                            var matchedExistingAgency = agencies.FirstOrDefault(ea => ea.Id == agency.Id);
                            if (matchedExistingAgency != null)
                            {
                                matchedExistingAgency.Adapt(agency);
                                agency.LeadsCount = originalLeadsCount;
                                agency.ProspectCount = originalProspectCount;
                            }
                            return agency;
                        }).ToList();
                        var dtos = agenciesWithCount?.Adapt<List<MarketingAgencyFormattedDto>>().ToList();
                        var dtos1 = dtos?.Adapt<List<MarketingAgencyV2FormattedDto>>().ToList();
                        foreach (var dto in dtos1)
                        {
                            var matchingHistory = dtos.FirstOrDefault(i => i.Name == dto.Name);
                            if (matchingHistory != null)
                            {
                                var matchedUser = users.FirstOrDefault(u => u.Id == matchingHistory.CreatedBy && !u.IsDeleted);
                                var lastModifiedUser = users.FirstOrDefault(u => u.Id == matchingHistory.LastModifiedBy && !u.IsDeleted);
                                if (matchedUser != null) { dto.CreatedBy = matchedUser.FirstName + ' ' + matchedUser.LastName; } else { dto.CreatedBy = string.Empty; }
                                if (lastModifiedUser != null) { dto.LastModifiedBy = lastModifiedUser.FirstName + ' ' + lastModifiedUser.LastName ?? string.Empty; } else { dto.LastModifiedBy = string.Empty; }


                            }

                        }
                        var fileBytes = ExcelGeneration<MarketingAgencyV2FormattedDto>.GenerateExcel(dtos1, "Marketing Agency", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Marketing_Agency", $"Export_Marketing_Agency_" + input.TenantId + request.FileName + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await SendExportEmailAsync(presignedUrl, tracker, exportEmailTemplate, serviceProvider);
                        isSent = true;
                        tracker.Count = agenciesWithCount.Count();
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Marketing_Agency_" + request.FileName + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportMarketingTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportMarketingTrackerRepo.UpdateAsync(tracker);
                    if (errorEmailTemplate != null && !isSent && serviceProvider != null)
                    {
                        await SendExceptionEmailAsync(tracker, errorEmailTemplate, serviceProvider);
                    }
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                    ErrorModule = "FunctionEntryPoint -> ExportMarketingAgencyHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

        }
        public async Task ExportMarketingChannelPartnerHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportMarketingTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
            var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
            var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ExportLead), CancellationToken.None)).FirstOrDefault();
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            List<Lrb.Application.Identity.Users.UserDetailsDto> users = new(await _userService.GetListAsync(cancellationToken));
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);

            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };

            try
            {
                if (tracker != null && serviceProvider != null && exportEmailTemplate != null)
                {
                    RunAWSBatchForMarketingChannelPartnerRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForMarketingChannelPartnerRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var cpCount = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<ViewChannelPartnerDto>("LeadratBlack", "ChannelPartners_Affiliated_Count", new
                        {
                            tenant_id = tenantId,
                            channelpartners = request.SearchByName?.Select(name => name.Replace(" ", "").ToLower()).ToList(),
                            leads_min = request.LeadsMinCount,
                            leads_max = request.LeadsMaxCount,
                            prospects_min = request.ProspectMinCount,
                            prospects_max = request.ProspectMaxCount
                        }))?.ToList() ?? new List<ViewChannelPartnerDto>();
                        var channelPartners = await _cpRepository.ListAsync(new ExportChannelPartnerByFiltersSpec(request, cpCount.Select(a => a.Id).ToList()), cancellationToken);
                        request.PageSize = int.MaxValue;
                        var totalCount = await _cpRepository.CountAsync(new ExportChannelPartnerByFiltersSpec(request, cpCount.Select(a => a.Id).ToList()), cancellationToken);
                        var channelPartnersWithCount = cpCount
                        .Where(agency => channelPartners.Any(ea => ea.Id == agency.Id))
                        .Select(agency =>
                        {
                            var originalLeadsCount = agency.LeadsCount;
                            var originalProspectCount = agency.ProspectCount;
                            var matchedExistingAgency = channelPartners.FirstOrDefault(ea => ea.Id == agency.Id);
                            if (matchedExistingAgency != null)
                            {
                                matchedExistingAgency.Adapt(agency);
                                agency.LeadsCount = originalLeadsCount;
                                agency.ProspectCount = originalProspectCount;
                            }
                            return agency;
                        }).ToList();
                        var dtos = channelPartnersWithCount?.Adapt<List<MarketingCpFormattedDto>>().ToList();
                        var dtos1 = channelPartnersWithCount?.Adapt<List<MarketingCpV2FormattedDto>>().ToList();

                        foreach (var dto in dtos1)
                        {
                            var matchingHistory = dtos.FirstOrDefault(i => i.FirmName == dto.FirmName);
                            if (matchingHistory != null)
                            {
                                var matchedUser = users.FirstOrDefault(u => u.Id == matchingHistory.CreatedBy && !u.IsDeleted);
                                var lastModifiedUser = users.FirstOrDefault(u => u.Id == matchingHistory.LastModifiedBy && !u.IsDeleted);
                                if (matchedUser != null) { dto.CreatedBy = matchedUser.FirstName + ' ' + matchedUser.LastName; } else { dto.CreatedBy = string.Empty; }
                                if (lastModifiedUser != null) { dto.LastModifiedBy = lastModifiedUser.FirstName + ' ' + lastModifiedUser.LastName ?? string.Empty; } else { dto.LastModifiedBy = string.Empty; }


                            }
                            // var matchedUser = users.FirstOrDefault(u => u.Id == dto.CreatedBy && !u.IsDeleted);


                        }
                        var fileBytes = ExcelGeneration<MarketingCpV2FormattedDto>.GenerateExcel(dtos1, "Marketing ChannelPartner", formattedFiltersDto, exportTracker,request.TimeZoneId, request.BaseUTcOffset);
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Marketing_ChannelPartner", $"Export_Marketing_ChannelPartner_" + input.TenantId + request.FileName + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await SendExportEmailAsync(presignedUrl, tracker, exportEmailTemplate, serviceProvider);
                        isSent = true;
                        tracker.Count = channelPartnersWithCount.Count();
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Marketing_ChannelPartner_" + request.FileName + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportMarketingTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportMarketingTrackerRepo.UpdateAsync(tracker);
                    if (errorEmailTemplate != null && !isSent && serviceProvider != null)
                    {
                        await SendExceptionEmailAsync(tracker, errorEmailTemplate, serviceProvider);
                    }
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                    ErrorModule = "FunctionEntryPoint -> ExportMarketingChannelPartnerHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

        }

        public async Task ExportMarketingCampaignHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportMarketingTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
            var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
            var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ExportLead), CancellationToken.None)).FirstOrDefault();
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            List<Lrb.Application.Identity.Users.UserDetailsDto> users = new(await _userService.GetListAsync(cancellationToken));
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);

            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };

            try
            {
                if (tracker != null && serviceProvider != null && exportEmailTemplate != null)
                {
                    RunAWSBatchForMarketingCampaignRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForMarketingCampaignRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var campaignCount = (await _dapperRepository.QueryStoredProcedureAsync<ViewCampaignDto>("LeadratBlack", "Campaigns_Affiliated_Count", new
                        {
                            tenant_id = tenantId,
                            campaigns = request.CampaignNames?.Select(name => name.Replace(" ", "").ToLower()).ToList(),
                            leads_min = request.LeadsMinCount,
                            leads_max = request.LeadsMaxCount,
                            prospects_min = request.ProspectMinCount,
                            prospects_max = request.ProspectMaxCount
                        }))?.ToList() ?? new List<ViewCampaignDto>();
                        var campaigns = await _campaignRepo.ListAsync(new ExportCampaignsByFiltersSpec(request, campaignCount.Select(a => a.Id).ToList()), cancellationToken);
                        request.PageSize = int.MaxValue;
                        var totalCount = await _campaignRepo.CountAsync(new ExportCampaignsByFiltersSpec(request, campaignCount.Select(a => a.Id).ToList()), cancellationToken);
                        var campaignsWithCount = campaignCount
                        .Where(campaign => campaigns.Any(ea => ea.Id == campaign.Id))
                        .Select(campaign =>
                        {
                            var originalLeadsCount = campaign.LeadsCount;
                            var originalProspectCount = campaign.ProspectCount;
                            var matchedExistingCampaign = campaigns.FirstOrDefault(ea => ea.Id == campaign.Id);
                            if (matchedExistingCampaign != null)
                            {
                                matchedExistingCampaign.Adapt(campaign);
                                campaign.LeadsCount = originalLeadsCount;
                                campaign.ProspectCount = originalProspectCount;
                            }
                            return campaign;
                        }).ToList();
                        var dtos = campaignsWithCount?.Adapt<List<MarketingCampaignFormattedDto>>().ToList();
                        var dtos1 = dtos?.Adapt<List<MarketingCampaignV2FormattedDto>>().ToList();
                        foreach (var dto in dtos1)
                        {
                            var matchingHistory = dtos.FirstOrDefault(i => i.Name == dto.Name);
                            if (matchingHistory != null)
                            {
                                var matchedUser = users.FirstOrDefault(u => u.Id == matchingHistory.CreatedBy && !u.IsDeleted);
                                var lastModifiedUser = users.FirstOrDefault(u => u.Id == matchingHistory.LastModifiedBy && !u.IsDeleted);
                                if (matchedUser != null) { dto.CreatedBy = matchedUser.FirstName + ' ' + matchedUser.LastName; } else { dto.CreatedBy = string.Empty; }
                                if (lastModifiedUser != null) { dto.LastModifiedBy = lastModifiedUser.FirstName + ' ' + lastModifiedUser.LastName ?? string.Empty; } else { dto.LastModifiedBy = string.Empty; }


                            }

                        }
                        var fileBytes = ExcelGeneration<MarketingAgencyV2FormattedDto>.GenerateExcel(dtos1, "Marketing Camapign", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Marketing_Campaign", $"Export_Marketing_Campaign_" + input.TenantId + request.FileName + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await SendExportEmailAsync(presignedUrl, tracker, exportEmailTemplate, serviceProvider);
                        isSent = true;
                        tracker.Count = campaignsWithCount.Count();
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Marketing_Campaign_" + request.FileName + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportMarketingTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportMarketingTrackerRepo.UpdateAsync(tracker);
                    if (errorEmailTemplate != null && !isSent && serviceProvider != null)
                    {
                        await SendExceptionEmailAsync(tracker, errorEmailTemplate, serviceProvider);
                    }
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                    ErrorModule = "FunctionEntryPoint -> ExportMarketingCampaignHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

        }



        private async Task SendExportEmailAsync(string presignedUrl, ExportMarketingTracker tracker, MasterEmailTemplates exportEmailTemplate, MasterEmailServiceProvider serviceProvider)
        {
            EmailSenderDto emailSenderDto = new EmailSenderDto();
            List<string> toEmails = new();
            List<string> ccEamils = new();
            List<string> bccEamils = new();
            if (tracker?.ToRecipients?.Any() ?? false)
            {
                toEmails.AddRange(tracker.ToRecipients);
            }
            if (tracker?.CcRecipients?.Any() ?? false)
            {
                ccEamils.AddRange(tracker.CcRecipients);
            }
            if (tracker?.BccRecipients?.Any() ?? false)
            {
                bccEamils.AddRange(tracker.BccRecipients);
            }
            var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
            emailSenderDto.To = toEmails;
            emailSenderDto.Cc = ccEamils;
            emailSenderDto.Bcc = bccEamils;
            emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
            emailSenderDto.EmailBody = template;
            emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
            emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
            await _graphEmailService.SendEmail(emailSenderDto);

        }
        private async Task SendExceptionEmailAsync(ExportMarketingTracker tracker, MasterEmailTemplates errorEmailTemplate, MasterEmailServiceProvider serviceProvider)
        {
            if (errorEmailTemplate != null && serviceProvider != null)
            {
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                List<string> toEmails = new();
                List<string> ccEamils = new();
                List<string> bccEamils = new();
                if (tracker?.ToRecipients?.Any() ?? false)
                {
                    toEmails.AddRange(tracker.ToRecipients);
                }
                if (tracker?.CcRecipients?.Any() ?? false)
                {
                    ccEamils.AddRange(tracker.CcRecipients);
                }
                if (tracker?.BccRecipients?.Any() ?? false)
                {
                    bccEamils.AddRange(tracker.BccRecipients);
                }
                emailSenderDto.To = toEmails;
                emailSenderDto.Cc = ccEamils;
                emailSenderDto.Bcc = bccEamils;
                emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                emailSenderDto.EmailBody = errorEmailTemplate.Body;
                emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                emailSenderDto.Subject = errorEmailTemplate?.Subject ?? string.Empty;
                await _graphEmailService.SendEmail(emailSenderDto);
            }
        }
    }
}
