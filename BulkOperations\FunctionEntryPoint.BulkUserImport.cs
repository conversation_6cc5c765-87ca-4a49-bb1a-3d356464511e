﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.TimeZone.Dto;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.User;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using NodaTime;
using NodaTime.TimeZones;
using System.Data;
using System.Text.RegularExpressions;
namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public const string DefaultPassword = "123Pa$$word!";
        public async Task BulkUserImportHandler(InputPayload input)
        {

            CancellationToken cancellationToken = CancellationToken.None;
            List<InvalidUserDto> invalidUser = new List<InvalidUserDto>();
            BulkUserUploadTracker tracker = await _userImportRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            try
            {
                if (tracker != null)
                {
                    tracker.MappedColumnData = tracker.MappedColumnData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                    tracker.Status = UploadStatus.Started;
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _userImportRepo.UpdateAsync(tracker);

                    var globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec());
                    string Code = globalSettings?.Countries?.FirstOrDefault()?.Code;
                    DataTable dataTable = new();
                    int totalRows = 0;
                    UploadType uploadType = UploadType.None;
                    if (!string.IsNullOrWhiteSpace(tracker.S3BucketKey))
                    {
                        Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", tracker.S3BucketKey);
                        if (tracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                        {
                            using MemoryStream memoryStream = new();
                            fileStream.CopyTo(memoryStream);
                            dataTable = CSVHelper.CSVToDataTable(memoryStream);
                            uploadType = UploadType.CSV;
                        }
                        else
                        {
                            dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, tracker.SheetName);
                            uploadType = UploadType.Excel;
                        }
                        totalRows = dataTable.Rows.Count;
                        for (int i = totalRows - 1; i >= 0; i--)
                        {
                            DataRow row = dataTable.Rows[i];
                            if (row.ItemArray.All(i => string.IsNullOrEmpty(i?.ToString())))
                            {
                                row.Delete();
                            }
                        }
                        if (dataTable.Rows.Count <= 0)
                        {
                            throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                        }
                        totalRows = dataTable.Rows.Count;
                        tracker.TotalCount = totalRows;
                        Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                    }
                    List<DuplicateUsersDto> duplicateUsersDto = new List<DuplicateUsersDto>();
                    DuplicateUsersDto duplicateUsers = new DuplicateUsersDto();


                    List<CreateUserRequest> users = new List<CreateUserRequest>();
                    List<Department>? departmentList = new List<Department>();
                    List<Designation>? designationList = new List<Designation>();
                    foreach (DataRow row in dataTable.Rows)
                    {
                        try
                        {
                            CreateUserRequest userDetails = new CreateUserRequest();

                            #region Name Validation
                            userDetails.FirstName = row["FirstName"].ToString();
                            if (string.IsNullOrWhiteSpace(userDetails.FirstName))
                            {
                                var invalidUserData = userDetails.Adapt<InvalidUserDto>();
                                invalidUserData.Errors = "FirstName can not be null!";
                                invalidUser.Add(invalidUserData);
                            }
                            userDetails.LastName = row["LastName"].ToString();
                            if (string.IsNullOrWhiteSpace(userDetails.LastName))
                            {
                                var invalidUserData = userDetails.Adapt<InvalidUserDto>();
                                invalidUserData.Errors = "LastName can not be null!";
                                invalidUser.Add(invalidUserData);
                            }
                            userDetails.UserName = row["UserName"].ToString();
                            if (string.IsNullOrWhiteSpace(userDetails?.UserName?.Trim()))
                            {
                                var invalidUserData = userDetails.Adapt<InvalidUserDto>();
                                invalidUserData.Errors = "UserName can not be null!";
                                invalidUser.Add(invalidUserData);
                            }
                            if (userDetails.UserName.Contains(" "))
                            {
                                var invalidUserData = userDetails.Adapt<InvalidUserDto>();
                                invalidUserData.Errors = "UserName can not contain a space character";
                                invalidUser.Add(invalidUserData);
                            }
                            if (!string.IsNullOrWhiteSpace(userDetails.UserName))
                            {
                                if (await _userService.ExistsWithNameAsync(userDetails.UserName))
                                {
                                    duplicateUsers.UserName = userDetails.UserName;
                                    var invalidUserData = userDetails.Adapt<InvalidUserDto>();
                                    invalidUserData.Errors = $"UserName already exists in excel sheet! {userDetails.UserName}";
                                    invalidUser.Add(invalidUserData);

                                }
                            }
                            #endregion
                            #region PhoneNumber Validation
                            string phoneNumber = row["PhoneNumber"].ToString() ?? string.Empty;
                            string CountryCode = row["CountryCode"].ToString() ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode;
                            if (!string.IsNullOrWhiteSpace(phoneNumber))
                            {
                                userDetails.PhoneNumber = ListingSitesHelper.ConcatenatePhoneNumberV2(CountryCode, phoneNumber, globalSettings, null);
                            }
                            else
                            {
                                var invalidUserData = userDetails.Adapt<InvalidUserDto>();
                                invalidUserData.Errors = "PhoneNumber can not be null!";
                                invalidUser.Add(invalidUserData);
                            }
                            if (!string.IsNullOrWhiteSpace(userDetails.PhoneNumber) && !userDetails.PhoneNumber.StartsWith("+"))
                            {
                                var invalidUserData = userDetails.Adapt<InvalidUserDto>();
                                invalidUserData.Errors = $"invalid phonenumber! {userDetails.PhoneNumber}";
                                invalidUser.Add(invalidUserData);
                            }
                            if (!string.IsNullOrWhiteSpace(userDetails.PhoneNumber))
                            {
                                if (await _userService.ExistsWithPhoneNumberAsync(userDetails.PhoneNumber.Trim()))
                                {
                                    duplicateUsers.PhoneNumber = userDetails.PhoneNumber;
                                    var invalidUserData = userDetails.Adapt<InvalidUserDto>();
                                    invalidUserData.Errors = $"PhoneNumber already exists in excel sheet! {userDetails.PhoneNumber}";
                                    invalidUser.Add(invalidUserData);
                                }
                            }

                            string PersonalPhoneNo = row["PersonalPhoneNo"].ToString() ?? string.Empty;
                            if (!string.IsNullOrWhiteSpace(PersonalPhoneNo))
                            {
                                userDetails.AltPhoneNumber = ListingSitesHelper.ConcatenatePhoneNumberV2(CountryCode, PersonalPhoneNo, globalSettings, null);
                            }
                            #endregion
                            #region Email Validation
                            userDetails.Email = row["Email"].ToString();
                            if (!string.IsNullOrWhiteSpace(userDetails.Email.Trim()))
                            {
                                if (Regex.IsMatch(userDetails.Email, RegexPatterns.EmailPattern))
                                {
                                    userDetails.Email = row["Email"].ToString();
                                }
                            }
                            else
                            {
                                var invalidUserData = userDetails.Adapt<InvalidUserDto>();
                                invalidUserData.Errors = $"Email can not be null! {userDetails.Email}";
                                invalidUser.Add(invalidUserData);
                            }
                            if (!string.IsNullOrWhiteSpace(userDetails.Email))
                            {
                                if (await _userService.ExistsWithEmailAsync(userDetails.Email.Trim()))
                                {
                                    duplicateUsers.Email = userDetails.Email;
                                    var invalidUserData = userDetails.Adapt<InvalidUserDto>();
                                    invalidUserData.Errors = $"Email already exist in excel sheet! {userDetails.Email}";
                                    invalidUser.Add(invalidUserData);

                                }
                            }

                            #endregion

                            userDetails.Address = row["Address"].ToString();
                            try
                            {
                                if (string.IsNullOrWhiteSpace(row["BloodGroupType"].ToString()))
                                {
                                    userDetails.BloodGroup = BloodGroupType.None;
                                }
                                else
                                {
                                    userDetails.BloodGroup = Enum.Parse<BloodGroupType>(row["BloodGroupType"].ToString());
                                }
                            }
                            catch { }

                            try
                            {

                                if (string.IsNullOrWhiteSpace(row["Gender"].ToString()))
                                {
                                    userDetails.Gender = Gender.NotMentioned;
                                }
                                else
                                {
                                    userDetails.Gender = Enum.Parse<Gender>(row["Gender"].ToString());
                                }
                            }
                            catch
                            {

                            }
                            if (!string.IsNullOrWhiteSpace(row["Department"].ToString()))
                            {
                                departmentList = await _departmentRepo.ListAsync(new GetAllDepartmentsSpec());
                                string departments = row["Department"].ToString();
                                var department = departmentList.FirstOrDefault(u => u.Name.ToLower() == departments.ToLower().Trim().Replace(" ", ""));


                                if (department != null)
                                {
                                    userDetails.DepartmentId = department.Id;
                                }
                                else
                                {
                                    Lrb.Domain.Entities.Department departmentv1 = new()
                                    {
                                        Name = departments,

                                    };
                                    await _departmentRepo.AddAsync(departmentv1);
                                    userDetails.DepartmentId = departmentv1.Id;

                                }
                            }
                            if (!string.IsNullOrWhiteSpace(row["Designation"].ToString()))
                            {
                                designationList = await _designationRepo.ListAsync(new GetAllDesignationSpec());
                                string designatons = row["Designation"].ToString();
                                var designation = designationList.FirstOrDefault(u => u.Name.ToLower() == designatons.ToLower().Trim().Replace(" ", ""));

                                var designationsName = designationList.Select(i => i.Name.ToLower()).ToList();
                                if (designationsName.Contains(designatons.ToLower().Trim().Replace(" ", "")))
                                {
                                    userDetails.DesignationId = designation.Id;
                                }
                                else
                                {
                                    Designation designationsv1 = new()
                                    {
                                        Name = designatons,

                                    };
                                    await _designationRepo.AddAsync(designationsv1);
                                    userDetails.DesignationId = designationsv1.Id;

                                }
                            }
                            List<Lrb.Application.Identity.Users.UserDetailsDto> usersList = new(await _userService.GetListAsync(cancellationToken));

                            if (!string.IsNullOrWhiteSpace(row["ReportTo"].ToString()))
                            {
                                var username = usersList.Select(i => i.UserName.ToLower()).ToList();
                                string user = row["ReportTo"].ToString();
                                var assignToUser = usersList.FirstOrDefault(u => u.UserName.ToLower() == user.ToLower().Trim().Replace(" ", ""));

                                if (assignToUser != null)
                                {
                                    userDetails.ReportsTo = assignToUser.Id;
                                }
                                else
                                {
                                    userDetails.ReportsTo = _currentUser.GetUserId();

                                }
                            }

                            try
                            {
                                var brokerNumber = row["BrokerNumber"]?.ToString();

                                userDetails.LicenseNo = !string.IsNullOrWhiteSpace(brokerNumber) ? brokerNumber : null;

                            }
                            catch (Exception ex)
                            {
                            }

                            if (!string.IsNullOrWhiteSpace(row["GeneralManager"].ToString()))
                            {
                                var username = usersList.Select(i => i.UserName.ToLower()).ToList();
                                string user = row["GeneralManager"].ToString();
                                var assignToUser = usersList.FirstOrDefault(u => u.UserName.ToLower() == user.ToLower().Trim().Replace(" ", ""));


                                if (username.Contains(user.ToLower().Trim().Replace(" ", "")))
                                {
                                    userDetails.GeneralManager = assignToUser.Id;
                                }
                                else
                                {
                                    userDetails.ReportsTo = _currentUser.GetUserId();

                                }
                            }

                            userDetails.Description = row["Description"].ToString();
                            users.Add(userDetails);


                            if (duplicateUsers.UserName != null || duplicateUsers.Email != null || duplicateUsers.PhoneNumber != null)
                            {
                                duplicateUsersDto.Add(duplicateUsers);
                                users.RemoveAll(i => duplicateUsersDto.Select(i => i.UserName).Contains(i.UserName));
                                users.RemoveAll(i => duplicateUsersDto.Select(i => i.PhoneNumber).Contains(i.PhoneNumber));
                                users.RemoveAll(i => duplicateUsersDto.Select(i => i.Email).Contains(i.Email));

                            }
                        }

                        catch (Exception ex)
                        {
                        }
                    }

                    GetAllDuplicateItemsModel duplicates = new();
                    if (duplicateUsersDto.Any(i => i != null))
                    {
                        List<DuplicateUsersDto> duplicateItems = new();
                        duplicateUsersDto.ToList().ForEach(i => duplicateItems.Add(new DuplicateUsersDto(i.UserName, i.PhoneNumber, i.Email, DuplicateItemType.User)));
                        duplicates.DuplicateItems.AddRange(duplicateItems);
                        duplicates.Count = duplicateItems.Count;
                        duplicates.RequestedItemCount = dataTable.Rows.Count;
                        tracker.DuplicateCount = duplicateItems.Count;

                        await _userImportRepo.UpdateAsync(tracker);


                    }

                    if (invalidUser.Any())
                    {
                        byte[] bytes = ExcelHelper.CreateExcelFromList(invalidUser).ToArray();
                        string fileName = $"InvalidUsers-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                        string folder = "Users";
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        tracker.InvalidDataS3BucketKey = key;
                        tracker.InvalidCount = invalidUser.Count;
                        await _userImportRepo.UpdateAsync(tracker);

                    }
                    if (users.Count > 0)
                    {
                        users = users.Where(i => !string.IsNullOrWhiteSpace(i.LastName) && !string.IsNullOrWhiteSpace(i.FirstName)).ToList();
                        users = users.Where(u => u.PhoneNumber.StartsWith("+")).ToList();
                        users = users.DistinctBy(i => i.UserName).ToList();
                        users = users.DistinctBy(i => i.PhoneNumber).ToList();
                        users = users.DistinctBy(i => i.Email).ToList();
                    }

                    foreach (var user in users)
                    {
                        if (!string.IsNullOrWhiteSpace(user.Email) && !string.IsNullOrWhiteSpace(user.PhoneNumber) && !string.IsNullOrWhiteSpace(user.FirstName) && !string.IsNullOrWhiteSpace(user.LastName) && !string.IsNullOrWhiteSpace(user.UserName))
                        {

                            try
                            {
                                var timeZoneDto = GetTimeZoneForUsers(Code ?? "IN");
                                var department = departmentList.FirstOrDefault(i => i.Id == user.DepartmentId);
                                var designation = designationList.FirstOrDefault(i => i.Id == user.DesignationId);
                                user.Password = DefaultPassword;
                                user.ConfirmPassword = DefaultPassword;
                                var res = await _userService.CreateAsync(user, tracker?.Origin);
                                UserDetails userDetails = user.Adapt<UserDetails>();
                                userDetails.Designation = designation;
                                userDetails.Department = department;
                                userDetails.CurrentAddress = user.Address;
                                userDetails.IsAutomationEnabled = true;
                                userDetails.UserId = Guid.Parse(res.userId);
                                userDetails.TimeZoneInfo = JsonConvert.SerializeObject(timeZoneDto);
                                await _userDetailsRepository.AddAsync(userDetails);
                            }
                            catch (Exception ex)
                            {

                            }
                        }
                    }
                    tracker.TotalUploadedCount = users.Count;
                    tracker.Status = UploadStatus.Completed;
                    await _userImportRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception ex)
            {
                tracker.Status = UploadStatus.Failed;
                tracker.Message = ex.Message;
                await _userImportRepo.UpdateAsync(tracker);
            }
        }
        static CreateTimeZoneDto GetTimeZoneForUsers(string countryCode)
        {
            CreateTimeZoneDto timeZoneDto = new CreateTimeZoneDto();
            var locations = TzdbDateTimeZoneSource.Default.ZoneLocations;
            var ianaZoneId = locations?.FirstOrDefault(i => i.CountryCode == countryCode)?.ZoneId;
            var windowsZoneId = DateTimeZoneProviders.Tzdb.GetZoneOrNull(ianaZoneId)?.Id;

            if (!string.IsNullOrWhiteSpace(windowsZoneId))
            {
                try
                {
                    TimeZoneInfo windowsZone = TimeZoneInfo.FindSystemTimeZoneById(windowsZoneId);
                    string displayName = windowsZone.DisplayName;
                    TimeSpan baseUtcOffset = windowsZone.BaseUtcOffset;
                    timeZoneDto.TimeZoneDisplay = displayName;
                    timeZoneDto.TimeZoneName = windowsZoneId;
                    timeZoneDto.TimeZoneId = ianaZoneId;
                    timeZoneDto.BaseUTcOffset = baseUtcOffset;
                }
                catch (TimeZoneNotFoundException)
                {
                    Console.WriteLine($"Windows timezone not found for {ianaZoneId}");
                }
            }
            return timeZoneDto;

        }
    }
}



