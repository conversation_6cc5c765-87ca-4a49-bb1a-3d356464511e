﻿using Lrb.Application.Activity.Utils;
using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Team.Web;
using Lrb.Application.Team.Web.Requests;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Request;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.User;
using Mapster;
using Newtonsoft.Json;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ExportUserReportHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            ExportUserTracker? exportTracker = await _exportUserRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
            var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
            var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ExportLead), CancellationToken.None)).FirstOrDefault();
            EmailSenderDto emailSenderDto = new EmailSenderDto();
            bool isSent = false;

            Guid CreatredById = exportTracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? tracker = exportTracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            tracker.CreatedBy = createdBy;
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,
            };
            try
            {
                if (exportTracker != null && serviceProvider != null && exportEmailTemplate != null)
                {
                    GetAllUserInfoViewRequest? request = JsonConvert.DeserializeObject<GetAllUserInfoViewRequest>(exportTracker?.Request ?? string.Empty);

                    RunAWSBatchForUserReportRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForUserReportRequest>(exportTracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        UserFilterDto? filtersDto = request.Adapt<UserFilterDto>();
                        UserFormattedFilterDto formattedFiltersDto = filtersDto.Adapt<UserFormattedFilterDto>();
                        List<Guid> UsersByIds = request.UserIds;
                        List<Guid> ReportsToIds = request.ReportsToIds;
                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;
                        }
                        if (ReportsToIds != null && ReportsToIds.Count > 0)
                        {
                            List<string> UsersIdString = ReportsToIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.ReportsTo = userstodetails;
                        }
                        List<UserView>? viewUsers = new();
                        List<UserDetailsDto>? userDtos = new();
                        requestforFileName.FromDate = requestforFileName.FromDate.HasValue ? requestforFileName.FromDate.Value.ConvertFromDateToUtc() : null;
                        requestforFileName.ToDate = requestforFileName.ToDate.HasValue ? requestforFileName.ToDate.Value.ConvertToDateToUtc() : null;
                        viewUsers = await _userViewRepo.ListAsync(new UserViewByTimeZoneSpec(requestforFileName.Adapt<RunAWSBatchForUserReportRequest>()), cancellationToken);
                        userDtos = viewUsers.Adapt<List<UserDetailsDto>>();
                        var userLeadCountDtos = viewUsers.Select(i => new UserLeadCountDto() { UserId = i.Id, LeadCount = _leadRepo.CountAsync(new LeadsCountByUserIdSpec(i.Id), cancellationToken).Result }).ToList();
                        var userformattedDto = viewUsers.Join(userLeadCountDtos,
                            user => user.Id,
                            leadCount => leadCount.UserId,
                            (user, leadCount) => new ExportUserFormattedDto
                            {
                                Name = $"{user.FirstName} {user.LastName}",
                                ReportsTo = user.ReportsTo != null ? $"{user.ReportsTo.FirstName} {user.ReportsTo.LastName}" : "",
                                GeneralManager = user.GeneralManager != null ? $"{user.GeneralManager.FirstName} {user.GeneralManager.LastName}" : "",
                                Departments = user.Department != null ? user.Department.Name : "",
                                Designations = user.Designation != null ? user.Designation.Name : "",
                                UserRoles = string.Join(", ", user.UserRoles?.Where(role => role.Name != "Default").Select(role => role.Name) ?? new List<string>()),
                                LeadCount = leadCount.LeadCount,
                                IsActive = user.IsActive,
                                Email = user.Email,
                                PhoneNumbers = user.PhoneNumber,
                                AltPhoneNumber = user.AltPhoneNumber,
                                Location = user.Address,
                                CreatedOn = user.CreatedOn,
                                LastModifiedOn = user.LastModifiedOn
                            }).ToList();
                        var totalCount = viewUsers.Count();

                        var exportTemplate = await _exportTemplateRepo.GetByIdAsync(exportTracker?.TemplateId ?? Guid.Empty);
                        var fileBytes = Lrb.Application.Utils.ExcelGeneration<ExportUserFormattedDto>.GenerateExcel<ExportUserFormattedDto, UserFormattedFilterDto, ExportTrackerDto>(userformattedDto, "Export Users", formattedFiltersDto, tracker, requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset);
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Users/{input.TenantId ?? "Default"}", $"Export_Users_" + input.TenantId + requestforFileName.FileName + ".xlsx", fileBytes, 0);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        List<string> toEmails = new();
                        List<string> ccEamils = new();
                        List<string> bccEamils = new();
                        if (exportTracker?.ToRecipients?.Any() ?? false)
                        {
                            toEmails.AddRange(exportTracker.ToRecipients);
                        }
                        if (exportTracker?.CcRecipients?.Any() ?? false)
                        {
                            ccEamils.AddRange(exportTracker.CcRecipients);
                        }
                        if (exportTracker?.BccRecipients?.Any() ?? false)
                        {
                            bccEamils.AddRange(exportTracker.BccRecipients);
                        }
                        var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                        emailSenderDto.To = toEmails;
                        emailSenderDto.Cc = ccEamils;
                        emailSenderDto.Bcc = bccEamils;
                        emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                        emailSenderDto.EmailBody = template;
                        emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                        emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                        await _graphEmailService.SendEmail(emailSenderDto);
                        isSent = true;
                        exportTracker.Count = totalCount;
                        exportTracker.S3BucketKey = presignedUrl;
                        exportTracker.FileName = $"Export_Users_" + requestforFileName.FileName + ".xlsx";
                        exportTracker.Template = JsonConvert.SerializeObject(exportTemplate);
                        exportTracker.LastModifiedBy = input.CurrentUserId;
                        exportTracker.CreatedBy = input.CurrentUserId;
                        await _exportUserRepo.UpdateAsync(exportTracker, cancellationToken);

                    }

                }
            }
            catch (Exception ex)
            {

            }
        }

        public async Task ExportTeamReportHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            ExportUserTracker? exportTracker = await _exportUserRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
            var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
            var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ExportTeam), CancellationToken.None)).FirstOrDefault();
            EmailSenderDto emailSenderDto = new EmailSenderDto();
            bool isSent = false;

            Guid CreatredById = exportTracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? tracker = exportTracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            tracker.CreatedBy = createdBy;
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,
            };
            _logger.Information("Started");
            try
            {
                if (exportTracker != null && serviceProvider != null)
                {
                    RunAWSBatchForTeamReportRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForTeamReportRequest>(exportTracker?.Request ?? string.Empty);
                    if (request != null)
                    {

                        ExportTeamFormattedDto formattedFiltersDto = request.Adapt<ExportTeamFormattedDto>();
                        var team = await _teamRepo.FirstOrDefaultAsync(new TeamByIdSpec(request.TeamId ?? Guid.Empty));
                        var manager = await _userViewRepo.FirstOrDefaultAsync(new UserViewByIdSpec(team?.Manager ?? Guid.Empty));
                        var members = await _userViewRepo.ListAsync(new UserViewByIdSpec(team?.UserIds ?? new List<Guid>()));

                        List<TeamFormattedDto> teamFormattedDtos = new List<TeamFormattedDto>();

                        TeamFormattedDto managerDto = new();
                        managerDto = manager.Adapt<TeamFormattedDto>();
                        managerDto.TeamDesignation = "Team Leader";
                        managerDto.TeamName = team?.Name;
                        managerDto.NumberOfData = await _prospectRepo.CountAsync(new GetAssignedProspectByUserIdSpec(manager?.Id ?? new Guid()), cancellationToken);
                        managerDto.NumberOfLeads = await _leadRepo.CountAsync(new LeadsCountByUserIdSpec(manager?.Id ?? new Guid()), cancellationToken);

                        teamFormattedDtos.Add(managerDto);

                        foreach (var member in members)
                        {
                            TeamFormattedDto dto = new();
                            dto = member.Adapt<TeamFormattedDto>();
                            dto.TeamDesignation = "Team Member";
                            dto.TeamName = team?.Name;
                            dto.NumberOfData = await _prospectRepo.CountAsync(new GetAssignedProspectByUserIdSpec(member?.Id ?? new Guid()), cancellationToken);
                            dto.NumberOfLeads = await _leadRepo.CountAsync(new LeadsCountByUserIdSpec(member?.Id ?? new Guid()), cancellationToken);
                            teamFormattedDtos.Add(dto);
                        }
                        var exportTemplate = await _exportTemplateRepo.GetByIdAsync(exportTracker?.TemplateId ?? Guid.Empty);
                        var fileBytes = ActivityExcelHelper<ExportTeamFormattedDto>.GenerateExcel(teamFormattedDtos, "Export Team", new ExportTeamFormattedDto(), tracker, request.TimeZoneId, request.BaseUTcOffset);
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Users/{input.TenantId ?? "Default"}", $"Export-{DateTime.Now.ToString("F")}.xlsx", fileBytes, 0);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        exportTracker.Count = teamFormattedDtos.Count();
                        exportTracker.S3BucketKey = presignedUrl;
                        exportTracker.LastModifiedBy = input.CurrentUserId;
                        exportTracker.CreatedBy = input.CurrentUserId;
                        await _exportUserRepo.UpdateAsync(exportTracker, cancellationToken);

                    }
                }
            }
            catch (Exception ex)
            {

            }
        }

    }
}
