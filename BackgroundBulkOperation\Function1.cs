using Lrb.Application.DataManagement.Web.Request;
using Lrb.Application.DataManagement.Web.Request.Bulk_Upload;
using Lrb.Application.Lead.Web.Requests.UpdationRequests;
using Lrb.Application.ListingManagement.Web.Requests;
using Lrb.Application.Source.Web;
using Lrb.Shared.Extensions;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace BackgroundBulkOperation
{
    public static class Function1
    {
        #region BulkLeadAssignment
        [FunctionName("BulkLeadAssignment")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "BulkLeadAssignment/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            //log.LogInformation("C# HTTP trigger function processed a request.");
            return await ProcessAsync(req, log, env + "BulkLeadAssignment", env);
        }

        private static async Task<IActionResult> ProcessAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                //log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var request = JsonConvert.DeserializeObject<ProcessBulkLeadAssignmentRequest>(payload.Entity.ToString());
                        var isSucceeded = false;
                        var result = await _mediatR.Send(request);
                        isSucceeded = result.Succeeded;
                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "ProcessBulkOperationsAsync returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion

        #region Bulk Lead Status Update
        [FunctionName("BulkStatusUpdate")]
        public static async Task<IActionResult> RunJob(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "BulkStatusUpdate/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            //log.LogInformation("C# HTTP trigger function processed a request.");
            return await ProcessBulkStatusAsync(req, log, env + "BulkStatusUpdate", env);
        }

        private static async Task<IActionResult> ProcessBulkStatusAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                //log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var request = JsonConvert.DeserializeObject<ProcessBulkStatusUpdateRequest>(payload.Entity.ToString());
                        var isSucceeded = false;
                        var result = await _mediatR.Send(request);
                        isSucceeded = result.Succeeded;
                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "ProcessBulkStatusUpdateAsync returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion

        #region Sync Property From Listing Portal
        [FunctionName("SyncListedProperty")]
        public static async Task<IActionResult> RunSyncProperty(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "SyncListedProperty/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            log.LogInformation("C# HTTP trigger function processed a request.");
            return await SyncPropertyAsync(req, log, env + "SyncListedProperty", env);
        }

        private static async Task<IActionResult> SyncPropertyAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var request = JsonConvert.DeserializeObject<SyncPropertyFromListingPortalRequest>(payload.Entity.ToString());
                        var isSucceeded = false;
                        var result = await _mediatR.Send(request);
                        isSucceeded = result.Succeeded;
                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "ProcessBulkOperationsAsync returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }

        #endregion
        #region Bulk Prospect Assignment
        [FunctionName("BulkProspectAssignment")]
        public static async Task<IActionResult> RunAssignmentJobAsync(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "BulkProspectAssignment/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            log.LogInformation("C# HTTP trigger function processed a request.");
            return await ProcessBulkProspectAssignmentAsync(req, log, env + "BulkProspectAssignment", env);
        }

        private static async Task<IActionResult> ProcessBulkProspectAssignmentAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var request = JsonConvert.DeserializeObject<ProcessProspectBulkAssignment>(payload.Entity.ToString());
                        var isSucceeded = false;
                        var result = await _mediatR.Send(request);
                        isSucceeded = result.Succeeded;
                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "ProcessBulkProspectAssignmentAsync returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion
        #region Bulk Secondary Assignment
        [FunctionName("BulkSecondaryAssignment")]
        public static async Task<IActionResult> RunSecondaryAssignmentJobAsync(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "BulkSecondaryAssignment/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            log.LogInformation("C# HTTP trigger function processed a request.");
            return await ProcessBulkSecondaryAssignmentAsync(req, log, env + "BulkSecondaryAssignment", env);
        }

        private static async Task<IActionResult> ProcessBulkSecondaryAssignmentAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var request = JsonConvert.DeserializeObject<ProcessSecondaryLeadAsssignmentRequest>(payload.Entity.ToString());
                        var isSucceeded = false;
                        var result = await _mediatR.Send(request);
                        isSucceeded = result.Succeeded;
                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "ProcessBulkSecondaryAssignmentAsync returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion
        #region Bulk LeadSource Update
        [FunctionName("BulkLeadSourceUpdate")]
        public static async Task<IActionResult> RunBulkLeadSourceUpdateJobAsync(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "BulkLeadSourceUpdate/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            log.LogInformation("C# HTTP trigger function processed a request.");
            return await ProcessBulkLeadSourceUpdateAsync(req, log, env + "BulkLeadSourceUpdate", env);
        }

        private static async Task<IActionResult> ProcessBulkLeadSourceUpdateAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var request = JsonConvert.DeserializeObject<ProcessLeadSourceUpdateRequest>(payload.Entity.ToString());
                        var isSucceeded = false;
                        var result = await _mediatR.Send(request);
                        isSucceeded = result.Succeeded;
                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "ProcessBulkLeadSourceUpdateAsync returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion


        [FunctionName("BulkProspectStatusUpdate")]
        public static async Task<IActionResult> BulkProspectStatusUpdateAsync(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "BulkProspectStatusUpdate/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            log.LogInformation("C# HTTP trigger function processed a request.");
            return await ProcessProspectBulkStatusAsync(req, log, env + "BulkProspectStatusUpdate", env);
        }

        private static async Task<IActionResult> ProcessProspectBulkStatusAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var request = JsonConvert.DeserializeObject<ProcessBulkProspectStatusUpdateRequest>(payload.Entity.ToString());
                        var isSucceeded = false;
                        var result = await _mediatR.Send(request);
                        isSucceeded = result.Succeeded;
                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "ProcessProspectBulkStatusAsync returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }

        [FunctionName("BulkProspectSourceUpdate")]
        public static async Task<IActionResult> BulkProspectSourceUpdateAsync(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "BulkProspectSourceUpdate/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            log.LogInformation("C# HTTP trigger function processed a request.");
            return await ProcessProspectBulkSourceAsync(req, log, env + "BulkProspectSourceUpdate", env);
        }

        private static async Task<IActionResult> ProcessProspectBulkSourceAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var request = JsonConvert.DeserializeObject<ProcessProspectSourceUpdateRequest>(payload.Entity.ToString());
                        request.CurrentUserId = payload.CurrentUserId;
                        request.TenantId = payload.TenantId;
                        var isSucceeded = false;
                        var result = await _mediatR.Send(request);
                        isSucceeded = result.Succeeded;
                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "ProcessProspectSourceUpdateRequest returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }

        #region Update Source To Direct
        [FunctionName("UpdateSourceToDirect")]
        public static async Task<IActionResult> UpdateSourceToDirectAsync(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "UpdateSourceToDirect/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            log.LogInformation("C# HTTP trigger function processed a request.");
            return await ProcessUpdateSourceToDirectAsync(req, log, env + "UpdateSourceToDirect", env);
        }

        private static async Task<IActionResult> ProcessUpdateSourceToDirectAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var request = JsonConvert.DeserializeObject<UpdateSourceToDirectRequest>(payload.Entity.ToString());
                        request.CurrentUserId = payload.CurrentUserId;
                        request.TenantId = payload.TenantId;
                        var isSucceeded = false;
                        var result = await _mediatR.Send(request);
                        isSucceeded = result.Succeeded;
                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "UpdateSourceToDirectRequest returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion 

        [FunctionName("BulkProjectUpdate")]
        public static async Task<IActionResult> BulkProjectUpdateJobAsync(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "BulkProjectUpdate/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            log.LogInformation("C# HTTP trigger function processed a request.");
            return await ProcessBulkProjectUpdateAsync(req, log, env + "BulkProjectUpdate", env);
        }

        private static async Task<IActionResult> ProcessBulkProjectUpdateAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var request = JsonConvert.DeserializeObject<ProcessBulkProjectUpdateRequest>(payload.Entity.ToString());
                        var isSucceeded = false;
                        var result = await _mediatR.Send(request);
                        isSucceeded = result.Succeeded;
                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "ProcessBulkProspectAssignmentAsync returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }

        #region Facebook Conversion Api
        [FunctionName("FbConversionApi")]
        public static async Task<IActionResult> RunFbJob(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "FbConversionApi/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            //log.LogInformation("C# HTTP trigger function processed a request.");
            return await FbConversionAsync(req, log, env + "FbConversionApi", env);
        }
        private static async Task<IActionResult> FbConversionAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            try
            {
                // Read the request body
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    // Deserialize the payload and validate it
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var request = JsonConvert.DeserializeObject<Lrb.Application.Integration.Web.Dtos.FbConversionApiDto>(payload.Entity.ToString());
                        if (request != null)
                        {
                            using RestClient client = new();
                            foreach (var leadId in request.MetaLeadIds)
                            {
                                // Construct the payload body
                                var payloadBody = new
                                {
                                    data = new[]
                                    {
                                new
                                {
                                    event_name = request.StatusName ?? "new",
                                    event_time = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                                    action_source = "system_generated",
                                    user_data = new
                                    {
                                        lead_id = leadId.ToString()
                                    }
                                }
                            }
                                };

                                // Serialize the payload to JSON
                                var jsonPayload = JsonConvert.SerializeObject(payloadBody);
                                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                                // Build the request message
                                var requestMessage = new RestRequest($"https://graph.facebook.com/v21.0/{request.PixelId}/events?access_token={request.AccessToken}", Method.Post);
                                requestMessage.AddJsonBody(payloadBody);

                                // Execute the request
                                var response = await client.ExecuteAsync(requestMessage);

                                // Log success or failure based on the response status
                                if (response.IsSuccessful)
                                {
                                    log.LogInformation($"Response on success for lead ID {leadId}: {response.Content}");
                                }
                                else
                                {
                                    log.LogError($"Failed response for lead ID {leadId}: {response.Content} RequestBody : {requestBody}");
                                }
                            }
                        }
                        else
                        {
                            log.LogWarning($"Invalid FbConversionApiDto payload for function {functionName}");
                        }
                    }
                    else
                    {
                        log.LogWarning($"Failed to deserialize input payload for function {functionName}");
                    }
                }
                else
                {
                    log.LogWarning($"HTTP {functionName} trigger function Body is null or empty.");
                }

                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                // Log the exception and return an error message
                log.LogError($"Exception details while calling {functionName}: {ex.Message}");
                return new OkObjectResult($"HTTP {functionName} triggered function executed failed");
            }
        }
    }
    #endregion

}
