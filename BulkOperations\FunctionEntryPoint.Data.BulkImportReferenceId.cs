﻿using Lrb.Application.Property.Web;
using Lrb.Application.PropertyRefrenceInfomation.Web.Dtos;
using Lrb.Application.PropertyRefrenceInfomation.Web.Mapping;
using Lrb.Application.PropertyRefrenceInfomation.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Auth;
using Mapster;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Data;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task BulkImportReferenceIdHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _referenceIdTrackerRepo.FirstOrDefaultAsync(new GetAllBulkRefrenceIdUploadByIdSpecs(input.TrackerId));
            try
            {
                if (tracker != null)
                {
                    tracker.MappedColumnsData = tracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                    tracker.Status = UploadStatus.Started;
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;

                    await _referenceIdTrackerRepo.UpdateAsync(tracker);
                    Console.WriteLine($"FunctionEntryPoint() -> BulkImportReferenceIdHandler Updated Status: {tracker.Status} \n {JsonConvert.SerializeObject(tracker)}");

                    #region Convert to Datatable

                    Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", tracker.S3BucketKey);
                    DataTable dataTable = new();
                    if (tracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                    {
                        using MemoryStream memoryStream = new();
                        fileStream.CopyTo(memoryStream);
                        dataTable = CSVHelper.CSVToDataTable(memoryStream);
                    }
                    else
                    {
                        dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, tracker.SheetName);
                    }

                    int totalRows = dataTable.Rows.Count;
                    for (int i = totalRows - 1; i >= 0; i--)
                    {
                        var row = dataTable.Rows[i];
                        if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                        {
                            row.Delete();
                        }
                    }
                    if (dataTable.Rows.Count <= 0)
                    {
                        throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                    }
                    totalRows = dataTable.Rows.Count;
                    Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");

                    #endregion

                    #region Fetch All requires data
                    var existingRefInfos = await _referenceIdRepo.ListAsync(new GetAllRefrenceInfosForBulkUploadSpecs());
                    var listingSources = await _customListingSource.ListAsync(new GetAllListingSourceForRefrenceSpecs());
                    var assignmentModule = await _assignmentModuleRepo.FirstOrDefaultAsync(new GetAssignmentModuleForReferenceIdSpecs());
                    var lrbUsers = await _userViewRepo.ListAsync(new GetUserForReferenceIdAssignmentSpec());
                    #endregion

                    var referenceInfos = dataTable.ConvertToPropertyReferenceInfo(tracker.MappedColumnsData, listingSources, lrbUsers, assignmentModule);

                    #region Invalid Info
                    var invalidRefIds = new List<InvalidReferenceId>();
                    var existingReferences = existingRefInfos.Select(i => new { i.ReferenceId, i.ListingSource }).ToList();

                    referenceInfos.ForEach(i =>
                    {
                        if (!string.IsNullOrEmpty(i.ReferenceId))
                        {
                            var matchingRef = existingReferences
                                .FirstOrDefault(j => j.ReferenceId == i.ReferenceId && j.ListingSource?.Id == i.ListingSource?.Id);

                            if (matchingRef != null)
                            {
                                var invalidRefInfo = i.Adapt<InvalidReferenceId>();
                                invalidRefInfo.Errors = "Duplicate Reference Id for this Listing Source";
                                invalidRefIds.Add(invalidRefInfo);
                            }
                        }
                        else
                        {
                            var invalidRefInfo = i.Adapt<InvalidReferenceId>();
                            invalidRefInfo.Errors = "Empty Reference Id";
                            invalidRefIds.Add(invalidRefInfo);
                        }
                    });
                    #endregion

                    referenceInfos.RemoveAll(i => i.ReferenceId == string.Empty);

                    referenceInfos = referenceInfos.DistinctBy(i => i.ReferenceId).ToList();

                    #region Update Tracker
                    tracker.Status = UploadStatus.InProgress;
                    tracker.DistinctLeadCount = referenceInfos.Count();
                    tracker.TotalCount = totalRows;
                    await _referenceIdTrackerRepo.UpdateAsync(tracker);
                    #endregion

                    if (invalidRefIds?.Any() ?? false)
                    {
                        tracker.DuplicateCount = invalidRefIds.Where(i => i.Errors == "Duplicate Reference Id for this Listing Source").Count();
                        tracker.InvalidCount = invalidRefIds.Where(i => i.Errors == "Empty Reference Id").Count();
                        byte[] bytes = ReferenceInfoHelper.CreateExcelData(invalidRefIds).ToArray();
                        string fileName = $"InvalidLeads-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                        string folder = "Leads";
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        tracker.InvalidDataS3BucketKey = key;
                    }

                    if (referenceInfos.Count > 0)
                    {
                        int refIdsChunks = referenceInfos.Count > 5000 ? 5000 : referenceInfos.Count;
                        var chunks = referenceInfos.Chunk(refIdsChunks).Select(i => new ConcurrentBag<Lrb.Domain.Entities.PropertyReferenceInfo>(i));
                        var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);
                        var chunkIndex = 1;
                        foreach (var chunk in chunks.ToList())
                        {
                            var backgroundDto = new BulkReferenceIdUploadBackgroundDto()
                            {
                                TrackerId = tracker?.Id ?? Guid.Empty,
                                TenantInfoDto = tenantInfo,
                                CancellationToken = CancellationToken.None,
                                PropertyRefIds = new(chunk),
                                CurrentUserId = input.CurrentUserId,
                            };
                            if (chunkIndex == chunks.Count())
                            {
                                backgroundDto.IsLastChunk = true;
                            }
                            await ExecuteDBOperationsAsync(backgroundDto);
                            chunkIndex++;
                        }
                    }

                }
            }
            catch (Exception ex)
            {
            }
        }

        public async Task ExecuteDBOperationsAsync(BulkReferenceIdUploadBackgroundDto dto)
        {
            PropertyReferenceInfo listingAddress = new();
            var tracker = await _referenceIdTrackerRepo.FirstOrDefaultAsync(new GetAllBulkRefrenceIdUploadByIdSpecs(dto.TrackerId));
            try
            {
                await _referenceIdRepo.AddRangeAsync(dto.PropertyRefIds);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount = dto.PropertyRefIds.Count;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _referenceIdTrackerRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == tracker.InvalidCount)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _referenceIdTrackerRepo.UpdateAsync(tracker);
            }
        }
    }
}
