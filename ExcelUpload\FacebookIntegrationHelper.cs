﻿using Lrb.Application.Utils;


namespace ExcelUpload
{
    public static class FacebookIntegrationHelper
    {
        public static bool IsCretedOnSameTime(DateTime existingCreatedOn, DateTime? existingCreatedOnPortal, DateTime? newCreatedOnPortal)
        {
            bool isSame = false;
            if (newCreatedOnPortal == null)
            {
                isSame = true;
            }
            else if (existingCreatedOnPortal != null)
            {
                newCreatedOnPortal = newCreatedOnPortal.Value.ConvertAndSetKindAsUtc();
                var safeNewCreatedOnPortal = newCreatedOnPortal.Value.AddMinutes(330);
                var safeExistingCreatedOn = existingCreatedOn.AddMinutes(330);
                isSame =  existingCreatedOnPortal == newCreatedOnPortal 
                            || existingCreatedOnPortal == safeNewCreatedOnPortal 
                            || existingCreatedOn == newCreatedOnPortal 
                            || (existingCreatedOn > newCreatedOnPortal && existingCreatedOn <= newCreatedOnPortal.Value.AddMinutes(2));
            }
            else
            {
                newCreatedOnPortal = newCreatedOnPortal.Value.ConvertAndSetKindAsUtc();
                isSame = existingCreatedOn == newCreatedOnPortal || (existingCreatedOn > newCreatedOnPortal && existingCreatedOn <= newCreatedOnPortal.Value.AddMinutes(2));
            }
            return isSame;
        }
    }
}
