﻿using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Newtonsoft.Json;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint : FBCommonHandler, IFunctionEntryPoint
    {
        public async Task ExportFacebookBulkLeadsHandler(InputPayload input)
        {
            var tenantId = input.TenantId;
            var trackerId = input.TrackerId;
            var userId = input.CurrentUserId;
            if (!string.IsNullOrWhiteSpace(tenantId))
            {
                var tracker = await _exportFbBulkLeadsTrackerRepo.GetByIdAsync(trackerId);
                if (tracker != null)
                {
                    try
                    {
                        //tracker Update
                        tracker.LastModifiedBy = userId;
                        tracker.Status = UploadStatus.Started;
                        await _exportFbBulkLeadsTrackerRepo.UpdateAsync(tracker);
                        Console.WriteLine($"ExportFacebookBulkLeadsHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                        var subscribedAdsAndForms = await _facebookService.GetAdsAndFormsWithUserOrPageAccessTokenAsync(tenantId);
                        //tracker Update
                        tracker.ActiveAdsCount = subscribedAdsAndForms?.Count(i => i?.Type?.Contains("ad", StringComparison.InvariantCultureIgnoreCase) ?? false) ?? 0;
                        tracker.ActiveFormsCount = subscribedAdsAndForms?.Count(i => i?.Type?.Contains("form", StringComparison.InvariantCultureIgnoreCase) ?? false) ?? 0;
                        tracker.Status = UploadStatus.InProgress;
                        await _exportFbBulkLeadsTrackerRepo.UpdateAsync(tracker);
                        Console.WriteLine($"ExportFacebookBulkLeadsHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                        List<ExportFacebookBulkLeadsDto> bulkLeadsDtos = new();
                        if (subscribedAdsAndForms?.Any() ?? false)
                        {
                            var fromDate = tracker.FromDate;
                            var toDate = tracker.ToDate;
                            subscribedAdsAndForms = subscribedAdsAndForms.OrderBy(x => x.Type).ToList();
                            int fetchedLeadsCount = 0;
                            foreach (var adOrForm in subscribedAdsAndForms)
                            {
                                var integrationAccountInfo = (await _integrationAccInfoRepo.ListAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(adOrForm.FacebookAuthResponseId), default)).FirstOrDefault();
                                var fbBulkleadInfo = await _facebookService.GetBulkLeadInfoAsync(adOrForm.FacebookId ?? string.Empty, adOrForm.FacebookUserOrPageAccessToken ?? string.Empty, fromDate, toDate);
                                var fbLeads = fbBulkleadInfo?.data ?? new();
                                if (fbLeads?.Any() ?? false)
                                {
                                    //tracker Update
                                    fetchedLeadsCount += fbLeads.Count;
                                    tracker.FetchedLeadsCount = fetchedLeadsCount;
                                    tracker.Status = UploadStatus.InProgress;
                                    await _exportFbBulkLeadsTrackerRepo.UpdateAsync(tracker);
                                    Console.WriteLine($"ExportFacebookBulkLeadsHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                                    bulkLeadsDtos.AddRange(fbLeads.MapToExportDtos());
                                }
                            }
                        }
                        if (bulkLeadsDtos?.Any() ?? false)
                        {
                            var duplicateLeads = await _leadRepo.ListAsync(new TrueDuplicateLeadSpec(bulkLeadsDtos.Where(i => i.ContactNo.Length >= 10)?.Select(i => i.ContactNo ?? string.Empty)?.ToList() ?? new()), default);
                            var duplicateContactNos = duplicateLeads.Select(i => i.ContactNo).ToList();


                            bulkLeadsDtos.ForEach(i =>
                            {
                                if(duplicateContactNos.Any(j => i.ContactNo.Length >= 10 && j.Contains(i.ContactNo[^10..])))
                                {
                                    var existingLead = duplicateLeads.FirstOrDefault(k => i.ContactNo.Length >= 10 && (k.ContactNo?.Contains(i.ContactNo[^10..]) ?? false));
                                    i.Tag ??= duplicateContactNos.Any(j => i.ContactNo.Length >= 10 && (j.Contains(i.ContactNo[^10..]))) ? $"Duplicate, created on : {existingLead?.CreatedOn.AddMinutes(330)} " : string.Empty;
                                    i.SerialNumber ??= existingLead?.SerialNumber;
                                    i.ExistingSource ??= (existingLead?.Enquiries?.FirstOrDefault(i => i.IsPrimary) ?? existingLead?.Enquiries?.FirstOrDefault())?.LeadSource;
                                    i.ExistingSubSource ??= (existingLead?.Enquiries?.FirstOrDefault(i => i.IsPrimary) ?? existingLead?.Enquiries?.FirstOrDefault())?.SubSource;
                                }
                            });
                            var fileBytes = ExcelHelper.CreateExcelFromListAsByteArray(bulkLeadsDtos);
                            var fileName = $"BulkLeads-{DateTime.Now}.xlsx";
                            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", $"{tenantId}/Facebook", fileName, fileBytes);
                            //tracker Update
                            tracker.ExportedLeadsCount = bulkLeadsDtos.Count;
                            tracker.ExcelFileUrl = key;
                            tracker.Status = UploadStatus.Completed;
                            await _exportFbBulkLeadsTrackerRepo.UpdateAsync(tracker);
                            Console.WriteLine($"ExportFacebookBulkLeadsHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                        }
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine($"ExportFacebookBulkLeadsHandler() Error Message: {e?.InnerException?.Message ?? e?.Message}");
                        //tracker Update
                        tracker.Status = UploadStatus.Failed;
                        tracker.Error = JsonConvert.SerializeObject(e, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore });
                        await _exportFbBulkLeadsTrackerRepo.UpdateAsync(tracker);
                        var error = new LrbError()
                        {
                            ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                            ErrorSource = e?.Source,
                            StackTrace = e?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "FunctionEntryPoint -> ExportFacebookBulkLeadsHandler()",
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
        }
    }
}
