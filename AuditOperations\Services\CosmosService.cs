﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace AuditOperation
{
    public class CosmosService : ICosmosService
    {
        private readonly CosmosSettings _settings;
        private readonly CosmosClient _cosmosClient;

        public CosmosService(IOptions<CosmosSettings> options)
        {
            _settings = options.Value;
            _cosmosClient = new CosmosClient(_settings.EndpointUri, _settings.PrimaryKey);
        }
        public async Task<Database> CreateDatabaseAsync(string databaseId, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _cosmosClient.CreateDatabaseIfNotExistsAsync(databaseId, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                throw;
            }
        }
        public async Task<Container> InitCosmosDbAsync(CancellationToken cancellationToken = default)
        {
            return await GetContainer(_settings.ContainerName, "Lrb-AuditTrails");
        }

        public async Task<Container> CreateContainerAsync(Database database, string containerId, CancellationToken cancellationToken = default)
        {
            try
            {
                return await database.CreateContainerIfNotExistsAsync(containerId, "/partitionKey", cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                throw;
            }
        }
        public async Task<Container> GetContainer(string databaseId, string containerId, CancellationToken cancellationToken = default)
        {
            try
            {
                return _cosmosClient.GetContainer(databaseId, containerId);
            }
            catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
            {
                var database = await CreateDatabaseAsync(_settings.ContainerName);
                var container = await CreateContainerAsync(database, "Lrb-AuditTrails");
                return container;
            }
        }
        public async Task AddItemAsync<T>(T item, Container container, CancellationToken cancellationToken = default)
        {
            try
            {
                if (item is ICosmoItem cosmoItem)
                {
                    // Create an item in the container representing the Andersen family. Note we provide the value of the partition key for this item, which is "Andersen"
                    ItemResponse<T> response = await container.CreateItemAsync<T>(item, new PartitionKey(cosmoItem.PartitionKey));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n**************\n{ex.Message}");
                // ignore
            }
        }

        public async Task<T> AddItemToContainerAsync<T>(T item, Container container, CancellationToken cancellationToken = default)
        {
            try
            {
                if (item is ICosmoItem cosmoItem)
                {
                    try
                    {
                        // Read the item to see if it exists.  
                        ItemResponse<T> response = await container.ReadItemAsync<T>(cosmoItem.PartitionId, new PartitionKey(cosmoItem.PartitionKey));
                        return response.Resource;
                    }
                    catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
                    {
                        // Create an item in the container representing the Andersen family. Note we provide the value of the partition key for this item, which is "Andersen"
                        ItemResponse<T> response = await container.CreateItemAsync<T>(item, new PartitionKey(cosmoItem.PartitionKey));
                        return response.Resource;
                    }
                }
                else
                {
                    throw new InvalidCastException("This item can not be added in CosmosDb.");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n**************\n{ex.Message}");
                throw;
            }
        }

        public async Task<IEnumerable<T>> QueryItemsAsync<T>(string query, Container container, CancellationToken cancellationToken = default)
        {
            try
            {
                QueryDefinition queryDefinition = new(query);
                FeedIterator<T> queryResultSetIterator = container.GetItemQueryIterator<T>(queryDefinition);

                List<T> items = new();

                while (queryResultSetIterator.HasMoreResults)
                {
                    FeedResponse<T> currentResultSet = await queryResultSetIterator.ReadNextAsync();
                    foreach (T item in currentResultSet)
                    {
                        items.Add(item);
                        Console.WriteLine("\tRead {0}\n", item);
                    }
                }
                return items;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n**************\n{ex.Message}");
                throw;
            }
        }

        public async Task<T> ReplaceItemAsync<T>(T item, Container container, CancellationToken cancellationToken = default)
        {
            if (item is ICosmoItem cosmoItem)
            {
                try
                {
                    ItemResponse<T> response = await container.ReadItemAsync<T>(cosmoItem.PartitionId, new PartitionKey(cosmoItem.PartitionKey), cancellationToken: cancellationToken);
                    var itemBody = response.Resource;

                    itemBody = item;

                    response = await container.ReplaceItemAsync<T>(itemBody, cosmoItem.PartitionId, new PartitionKey(cosmoItem.PartitionKey), cancellationToken: cancellationToken);
                    return response.Resource;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"\n**************\n{ex.Message}");
                    throw;
                }
            }
            else
            {
                throw new InvalidDataException("This is not a cosmos item.");
            }
        }

        public async Task<bool> DeleteItemAsync<T>(T item, Container container, CancellationToken cancellationToken = default)
        {
            if (item is ICosmoItem cosmoItem)
            {
                try
                {
                    var partitionKeyValue = cosmoItem.PartitionKey;
                    var id = cosmoItem.PartitionId;
                    await container.DeleteItemAsync<T>(id, new PartitionKey(partitionKeyValue), cancellationToken: cancellationToken);
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"\n**************\n{ex.Message}");
                    throw;
                }
            }
            else
            {
                throw new InvalidDataException("This is not a cosmos item.");
            }
        }
    }
}
