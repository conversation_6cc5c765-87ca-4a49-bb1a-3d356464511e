﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DatabaseSettings": {
    "DBProvider": "postgresql",
    //"ConnectionString": "Host=lrb-prd-rds.cu22ll1a4z3g.ap-south-1.rds.amazonaws.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************;Pooling=true;MinPoolSize=3;MaxPoolSize=500;",
    "ConnectionString": "Host=lrb-prd.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;"
  }
}