using BackgroundTaskExecutors.DTOs;
using Lrb.Application.Attendance.Web.Requests;
using Lrb.Application.Lead.Mobile.Requests;
using Lrb.Application.Lead.Mobile.v2;
using Lrb.Application.ListingManagement.Web.Requests;
using Lrb.Shared.Extensions;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace BackgroundTaskExecutors
{
    public class Function1
    {
        static Semaphore _semaphoreOne = new Semaphore(1, 1);
        public static string Dev = "dev";
        public static string Qa = "qa";
        public static string Prd = "prd";

        //Deployment Keys

        //prd lrb-prd-background-operations queue
        //Connection = lrb_prd_background_operations_RootManageSharedAccessKey_SERVICEBUS

        //qa lrb-qa-background-operations queue
        //Connection = lrb_qa_background_operations_RootManageSharedAccessKey_SERVICEBUS

        //In this function we should not wright long running process
        [FunctionName("LrbBackgroundService")]
        public async Task Run([ServiceBusTrigger("lrb-qa-background-operations", Connection = "lrb_qa_background_operations_RootManageSharedAccessKey_SERVICEBUS")] string myQueueItem, ILogger log)
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? Qa;
            if (environmentName.Equals(Qa, StringComparison.InvariantCultureIgnoreCase))
            {
                _semaphoreOne.WaitOne();
                //log.LogInformation($"C# ServiceBus queue trigger function processed message: {myQueueItem}");
                try
                {
                    //Console.WriteLine($"Input received: {myQueueItem}");
                    var payload = JsonConvert.DeserializeObject<InputPayload>(myQueueItem);
                    var startup = new Startup(environmentName);
                    IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                    try
                    {
                        switch ((payload.Type ?? string.Empty).ToLower())
                        {
                            case "notification":
                                IFunctionEntryPoint _functionEntryPointOne = provider.GetRequiredService<IFunctionEntryPoint>();
                                var notificationDto = JsonConvert.DeserializeObject<SendNotificationDto>(payload.Entity.ToString());
                                await _functionEntryPointOne.ScheduleNotificationsAsync(notificationDto);
                                break;
                            case "leadhistory":
                                IFunctionEntryPoint _functionEntryPointTwo = provider.GetRequiredService<IFunctionEntryPoint>();
                                var leadhistory = JsonConvert.DeserializeObject<LeadHistoryDto>(payload.Entity.ToString());
                                leadhistory.CurrentUserId = payload.CurrentUserId;
                                await _functionEntryPointTwo.UpdateLeadHistoryAsync(leadhistory);
                                break;
                            case "assignleadsbasedonscenarios":
                                IFunctionEntryPoint _functionEntryPointThree = provider.GetRequiredService<IFunctionEntryPoint>();
                                var assignleadsbasedonscenarios = JsonConvert.DeserializeObject<V2AssignLeadsBasedOnScenariosRequest>(payload.Entity.ToString());
                                await _functionEntryPointThree.AssignLeadsBasedOnScenariosAsync(assignleadsbasedonscenarios);
                                break;
                            case "updateleadstatus":
                                IFunctionEntryPointMobile _mobileFunctionEntryPoint = provider.GetRequiredService<IFunctionEntryPointMobile>();
                                var updateleadstatus = JsonConvert.DeserializeObject<V2UpdateLeadStatusRequest>(payload.Entity.ToString());
                                await _mobileFunctionEntryPoint.UpdateLeadStatusAsync(updateleadstatus, payload.CurrentUserId);
                                break;
                            case "seedattendancesetting":
                                IFunctionEntryPoint _functionEntryPointFour = provider.GetRequiredService<IFunctionEntryPoint>();
                                var attendancesettings = JsonConvert.DeserializeObject<AttendanceSettingObject>(payload.Entity.ToString());
                                await _functionEntryPointFour.SeedAttendanceSettingAsync(attendancesettings, payload.CurrentUserId, payload.TenantId);
                                break;
                        }
                        _semaphoreOne.Release();
                    }
                    catch (Exception ex)
                    {
                        _semaphoreOne.Release();
                    }
                }
                catch (Exception e)
                {
                    _semaphoreOne.Release();
                    log.LogInformation($"C# ServiceBus exception details : {e.Serialize()}");
                    throw;
                }
            }
        }
        [FunctionName("AssignLeadsbasedOnScenarios")]
        public static async Task<IActionResult> AssignLeadsbasedOnScenarios(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "AssignLeadsbasedOnScenarios/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            try
            {
                //log.LogInformation("C# HTTP trigger function processed a request.");
                return await ProcessAsync(req, log, env + " AssignLeadsbasedOnScenarios", env, "AssignLeadsbasedOnScenarios");
            }
            catch (Exception)
            {
                return new OkObjectResult($"HTTP AssignLeadsbasedOnScenarios triggered function failed");
            }
        }
        [FunctionName("UpdateLeadStatus")]
        public static async Task<IActionResult> UpdateLeadStatus(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "UpdateLeadStatus/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            try
            {
                //log.LogInformation("C# HTTP trigger function processed a request.");
                return await ProcessAsync(req, log, env + " UpdateLeadStatus", env, "UpdateLeadStatus");
            }
            catch (Exception)
            {
                return new OkObjectResult($"HTTP UpdateLeadStatus triggered function failed");
            }
        }
        [FunctionName("Notification")]
        public static async Task<IActionResult> Notification(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "Notification/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            try
            {
                //log.LogInformation("C# HTTP trigger function processed a request.");
                return await ProcessAsync(req, log, env + " Notification", env, "Notification");
            }
            catch (Exception)
            {
                return new OkObjectResult($"HTTP Notification triggered function failed");
            }
        }
        [FunctionName("LeadHistory")]
        public static async Task<IActionResult> LeadHistory(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "LeadHistory/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            try
            {
                //log.LogInformation("C# HTTP trigger function processed a request.");
                return await ProcessAsync(req, log, env + " LeadHistory", env, "LeadHistory");
            }
            catch (Exception)
            {
                return new OkObjectResult($"HTTP LeadHistory triggered function failed");
            }
        }
        [FunctionName("StatusChangeNotification")]
        public static async Task<IActionResult> StatusChangeNotification(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "StatusChangeNotification/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            try
            {
                //log.LogInformation("C# HTTP trigger function processed a request.");
                return await ProcessAsync(req, log, env + " StatusChangeNotification", env, "StatusChangeNotification");
            }
            catch (Exception)
            {
                return new OkObjectResult($"HTTP LeadHistory triggered function failed");
            }
        }
        private static async Task<IActionResult> ProcessAsync(HttpRequest req, ILogger log, string functionName, string environmentName, string type)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            if (type == "AssignLeadsbasedOnScenarios")
            {
                try
                {
                    _semaphoreOne.WaitOne();
                    string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                    //log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                    if (!string.IsNullOrWhiteSpace(requestBody))
                    {
                        var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                        if (payload != null)
                        {
                            var startup = new Startup(environmentName);
                            IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                            IMediator _mediatR = provider.GetRequiredService<IMediator>();
                            var request = JsonConvert.DeserializeObject<V2AssignLeadsBasedOnScenariosRequest>(payload.Entity.ToString());
                            await _mediatR.Send(request);
                        }
                    }
                    else
                    {
                        log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                    }
                    _semaphoreOne.Release();
                    return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
                }
                catch (Exception ex)
                {
                    log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                    _semaphoreOne.Release();
                    return new OkObjectResult($"HTTP {functionName} triggered function failed");
                }
            }
            else if (type == "UpdateLeadStatus")
            {
                try
                {
                    _semaphoreOne.WaitOne();
                    string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                    //log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                    if (!string.IsNullOrWhiteSpace(requestBody))
                    {
                        var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                        if (payload != null)
                        {
                            var startup = new Startup(environmentName);
                            IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                            IFunctionEntryPointMobile _mobileFunctionEntryPoint = provider.GetRequiredService<IFunctionEntryPointMobile>();
                            var updateleadstatus = JsonConvert.DeserializeObject<V2UpdateLeadStatusRequest>(payload.Entity.ToString());
                            await _mobileFunctionEntryPoint.UpdateLeadStatusAsync(updateleadstatus, payload.CurrentUserId);
                        }
                    }
                    else
                    {
                        log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                    }
                    _semaphoreOne.Release();
                    return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
                }
                catch (Exception ex)
                {
                    log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                    _semaphoreOne.Release();
                    return new OkObjectResult($"HTTP {functionName} triggered function failed");
                }
            }
            else if (type == "LeadHistory")
            {
                try
                {
                    _semaphoreOne.WaitOne();
                    string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                    //log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                    if (!string.IsNullOrWhiteSpace(requestBody))
                    {
                        var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                        if (payload != null)
                        {
                            var startup = new Startup(environmentName);
                            IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                            IFunctionEntryPoint _functionEntryPointTwo = provider.GetRequiredService<IFunctionEntryPoint>();
                            var leadhistory = JsonConvert.DeserializeObject<LeadHistoryDto>(payload.Entity.ToString());
                            leadhistory.CurrentUserId = payload.CurrentUserId;
                            await _functionEntryPointTwo.UpdateLeadHistoryAsync(leadhistory);
                        }
                    }
                    else
                    {
                        log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                    }
                    _semaphoreOne.Release();
                    return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
                }
                catch (Exception ex)
                {
                    log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                    _semaphoreOne.Release();
                    return new OkObjectResult($"HTTP {functionName} triggered function failed");
                }
            }
            else if (type == "Notification")
            {
                try
                {
                    _semaphoreOne.WaitOne();
                    string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                    //log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                    if (!string.IsNullOrWhiteSpace(requestBody))
                    {
                        var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                        if (payload != null)
                        {
                            var startup = new Startup(environmentName);
                            IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                            IFunctionEntryPoint _functionEntryPointOne = provider.GetRequiredService<IFunctionEntryPoint>();
                            var notificationDto = JsonConvert.DeserializeObject<SendNotificationDto>(payload.Entity.ToString());
                            await _functionEntryPointOne.ScheduleNotificationsAsync(notificationDto);
                        }
                    }
                    else
                    {
                        log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                    }
                    _semaphoreOne.Release();
                    return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
                }
                catch (Exception ex)
                {
                    log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                    _semaphoreOne.Release();
                    return new OkObjectResult($"HTTP {functionName} triggered function failed");
                }
            }
            else if (type == "StatusChangeNotification")
            {
                try
                {
                    _semaphoreOne.WaitOne();
                    string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                    //log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                    if (!string.IsNullOrWhiteSpace(requestBody))
                    {
                        var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                        if (payload != null)
                        {
                            var startup = new Startup(environmentName);
                            IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                            IFunctionEntryPointMobileV2 _functionEntryPointFive = provider.GetRequiredService<IFunctionEntryPointMobileV2>();
                            var notificationDto = JsonConvert.DeserializeObject<UpdateStatusChangeDto>(payload.Entity.ToString());
                            await _functionEntryPointFive.SendStatusChangeNotificationAsync(notificationDto, payload.CurrentUserId, payload.TenantId);
                        }
                    }
                    else
                    {
                        log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                    }
                    _semaphoreOne.Release();
                    return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
                }
                catch (Exception ex)
                {
                    log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                    _semaphoreOne.Release();
                    return new OkObjectResult($"HTTP {functionName} triggered function failed");
                }
            }
            else
            {
                return new OkObjectResult($"HTTP {functionName} dosn't match with the all types");
            }
        }

        #region Assign PF Property Details
        [FunctionName("AssignPFProperty")]
        public static async Task<IActionResult> AssignPFProperty(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "AssignPFProperty/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            return await ProcessAssignPFPropertyAsync(req, log, env + " AssignPFProperty", env);
        }

        private static async Task<IActionResult> ProcessAssignPFPropertyAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayloadV2>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IFunctionEntryPoint _functionEntryPointOne = provider.GetRequiredService<IFunctionEntryPoint>();
                        var pfLeadPropertyDto = JsonConvert.DeserializeObject<LrbAssignPfPropertyDto>(payload.Entity.ToString());
                        await _functionEntryPointOne.AssignPFPropertyDetailsAsync(payload.TenantId, pfLeadPropertyDto);
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion

        #region Fetch Xml Listing From url
        [FunctionName("ReadXmlFeedListing")]
        public static async Task<IActionResult> ReadXmlFeedListing(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "ReadXmlFeedListing/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            return await ProcessXmlListingUrlAsync(req, log, env + " AssignPFProperty", env);
        }

        private static async Task<IActionResult> ProcessXmlListingUrlAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<XMLFeedInputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        IMediator _mediatR = provider.GetRequiredService<IMediator>();
                        var isSucceeded = false;
                        if (payload.Type.ToLower().Trim() == "propertyfinder")
                        {
                            var pfLeadPropertyDto = new FetchPFXmlListingFromUrlRequest() { Url = payload.URl, CurrentUserId = payload.CurrentUserId, TenantId = payload.TenantId, ListingSourecId = payload.ListingSourceId };
                            var result = await _mediatR.Send(pfLeadPropertyDto);
                            isSucceeded = result.Succeeded;
                        }
                        else if (payload.Type.ToLower().Trim() == "bayut" || payload.Type.ToLower().Trim() == "dubizzle")
                        {
                            var pfLeadPropertyDto = new FetchXmlFeedListingFOrBayutAndDubizzleFromUrlRequest() { Url = payload.URl, CurrentUserId = payload.CurrentUserId, TenantId = payload.TenantId };
                            var result = await _mediatR.Send(pfLeadPropertyDto);
                            isSucceeded = result.Succeeded;
                        }


                        _semaphoreOne.Release();
                        return new OkObjectResult(isSucceeded ? "This HTTP triggered function executed successfully" : "ReadXmlFeedListing returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling {functionName}:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion
    }
}
