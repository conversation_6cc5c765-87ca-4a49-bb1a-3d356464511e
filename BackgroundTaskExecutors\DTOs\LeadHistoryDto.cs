﻿using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BackgroundTaskExecutors.DTOs
{
    public class LeadHistoryDto
    {
        public object Lead { get; set; }
        public object? LeadDto { get; set; }
        public AppointmentType? AppointmentType { get; set; }
        public bool? ShouldUpdateContactRecord { get; set; }
        public bool IsCreatedFormMobile { get; set; }
        public Guid? CurrentUserId { get; set; }
    }
}
