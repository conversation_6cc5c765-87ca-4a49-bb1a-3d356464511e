{"DatabaseSettings": {"DBProvider": "postgresql", "ConnectionString": "Host=lrb-pgsql-sbx.cu22ll1a4z3g.ap-south-1.rds.amazonaws.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=gozrPtqDYNqsyDp78BQQ;Protocol=3;Pooling=true;MinPoolSize=3;MaxPoolSize=500;ConnectionIdleLifetime=1;", "ReadReplicaConnectionString": "Host=lrb-pgsql-sbx.cu22ll1a4z3g.ap-south-1.rds.amazonaws.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=gozrPtqDYNqsyDp78BQQ;Protocol=3;Pooling=true;MinPoolSize=3;MaxPoolSize=500;ConnectionIdleLifetime=1;"}}