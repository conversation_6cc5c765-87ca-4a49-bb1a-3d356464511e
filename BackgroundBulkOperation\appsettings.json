﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DatabaseSettings": {
    "DBProvider": "postgresql",
    "ConnectionString": "Host=dev-lrb.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;"
  }
}