﻿using Google.Apis.Auth.OAuth2;
using Google.Apis.Services;
using Google.Apis.Sheets.v4;
using Google.Apis.Sheets.v4.Data;
using Lrb.Application.Integration.Web;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.Integration.GoogleSheet;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using System.Data;
using System.Text.RegularExpressions;
using Lrb.Application.GlobalSettings.Web;
using System.Threading;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task GoogleSheetHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            //Initialize a Tracker 

            GoogleSpreadSheetTracker leadUploadTracker = await _googleSpreadSheetTrackerRepo.GetByIdAsync(input.TrackerId);
            Console.WriteLine($"handler() -> BulkLeadUploadTracker GetById(): {JsonConvert.SerializeObject(leadUploadTracker)}");
            try
            {
                if (leadUploadTracker != null)
                {
                    //leadUploadTracker.MappedColumnsData = leadUploadTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                    leadUploadTracker.LastModifiedBy = input.CurrentUserId;
                    leadUploadTracker.CreatedBy = input.CurrentUserId;
                    //var createType = leadUploadTracker.CreateType;
                    await _googleSpreadSheetTrackerRepo.UpdateAsync(leadUploadTracker);
                    Console.WriteLine($"handler() -> BulkLeadUploadTracker Updated Status: {leadUploadTracker.Status} \n {JsonConvert.SerializeObject(leadUploadTracker)}");
                    var googleSpreadSheet = await _spreadSheetRepo.GetByIdAsync(leadUploadTracker.SpreadSheetId) ?? throw new Exception("No googleSpreadSheet found by this sheet");
                    if (googleSpreadSheet != null)
                    {
                        var sheets = await _sheetRepo.ListAsync(new SheetSpec(googleSpreadSheet.Id));
                        var automatedSheets = sheets.Where(i => i.IsAutomated);
                        if (automatedSheets?.Any() ?? false)
                        {
                            leadUploadTracker = await _googleSpreadSheetTrackerRepo.GetByIdAsync(input.TrackerId);
                            leadUploadTracker.Status = UploadStatus.InProgress;
                            await _googleSpreadSheetTrackerRepo.UpdateAsync(leadUploadTracker);
                            //Create the Google Sheets credential
                            var account = (await _googelSheetIntegrationRepo.ListAsync(new GoogleSheetIntegrationDataSpec(googleSpreadSheet.GoogleSheetIntrgrationId))).FirstOrDefault();
                            Console.WriteLine($"GoogleSheetIntegrationData account ->   : " + JsonConvert.SerializeObject(account));
                            var tokenClass = await _googleAuthService.GetAuthUsingRefreshToken(account.RefreshTokenWeb);
                            Console.WriteLine($"account -> {account.Email}, tokenClass ->   : " + JsonConvert.SerializeObject(tokenClass));
                            if (tokenClass != null)
                            {
                                GoogleCredential credential = GoogleCredential.FromAccessToken(tokenClass.AccessToken)
                                    .CreateScoped(SheetsService.Scope.SpreadsheetsReadonly);

                                // Create the SheetsService
                                var sheetsService = new SheetsService(new BaseClientService.Initializer()
                                {
                                    HttpClientInitializer = credential,
                                    ApplicationName = "leadrat.com",
                                });
                                // Fetch the spreadsheet by spreadsheetId
                                leadUploadTracker = await _googleSpreadSheetTrackerRepo.GetByIdAsync(input.TrackerId);
                                foreach (var sheet in automatedSheets)
                                {
                                    string range = $"{sheet.Title}!A2:B";
                                    var sheetRequest = sheetsService.Spreadsheets.Values.Get(googleSpreadSheet.SpreadsheetId, sheet.Title);
                                    ValueRange response = sheetRequest.Execute();
                                    IList<IList<object>> values = response.Values;
                                    if (values != null && values.Count > 0)
                                    {
                                        DataTable dataTable = new();
                                        // Create columns based on the first row (assumed to be headers)
                                        foreach (var header in values[0])
                                        {
                                            dataTable.Columns.Add(header.ToString());
                                        }
                                        // Add rows to the DataTable
                                        for (int i = 1; i < values.Count; i++)
                                        {
                                            DataRow dataRow = dataTable.NewRow();
                                            for (int j = 0; j < values[i].Count; j++)
                                            {
                                                dataRow[j] = values[i][j];
                                            }
                                            dataTable.Rows.Add(dataRow);
                                        }
                                        List<InvalidData> invalids = new();
                                        int totalRows = dataTable.Rows.Count;
                                        for (int i = totalRows - 1; i >= 0; i--)
                                        {
                                            var row = dataTable.Rows[i];
                                            if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                                            {
                                                row.Delete();
                                            }
                                            else if (string.IsNullOrEmpty(row[sheet.MappedColumnsData[DataColumns.Name]].ToString()) && string.IsNullOrEmpty(row[sheet.MappedColumnsData[DataColumns.ContactNo]].ToString()))
                                            {
                                                var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i.ToString())));
                                                var invalidData = new InvalidData
                                                {
                                                    Errors = "contact number and name are empty",
                                                    Notes = notes
                                                };
                                                if (!invalids.Any(i => i.Notes == invalidData.Notes))
                                                {
                                                    invalids.Add(invalidData);
                                                }
                                                row.Delete();
                                            }
                                        }
                                        if (dataTable.Rows.Count > 0)
                                        {
                                            try
                                            {
                                                await ConvertAndStoreDataTableToDB(dataTable, sheet, invalids, input);
                                            }
                                            catch (Exception e)
                                            {
                                                Console.WriteLine("ConvertAndStoreDataTableToDB() -> exception : " + JsonConvert.SerializeObject(e));
                                            }
                                        }

                                    }
                                }
                                leadUploadTracker.Status = UploadStatus.Completed;
                                await _googleSpreadSheetTrackerRepo.UpdateAsync(leadUploadTracker);


                            }
                        }


                    }
                }
            }
            catch (Exception e)
            {
                leadUploadTracker.Status = UploadStatus.Failed;
                await _googleSpreadSheetTrackerRepo.UpdateAsync(leadUploadTracker);
                Console.WriteLine("() -> exception : " + JsonConvert.SerializeObject(e));
            }

        }
        public async Task<bool> ConvertAndStoreDataTableToDB(DataTable dataTable,
           Lrb.Domain.Entities.Integration.GoogleSheet.Sheet sheet,
           List<InvalidData> invalids, InputPayload input)
        {
            GoogleSheetUploadTracker leadUploadTracker = await _googleSheetTrackerRepo.FirstOrDefaultAsync(new GoogleSheetUploadTrackerSpec(sheet.Id));
            if (leadUploadTracker != null)
            {
                leadUploadTracker.MappedColumnsData = leadUploadTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                leadUploadTracker.LastModifiedBy = input.CurrentUserId;
                leadUploadTracker.CreatedBy = input.CurrentUserId;
                leadUploadTracker.Status = UploadStatus.Started;
                Console.WriteLine($"ConvertAndStoreDataTableToDB() -> called");
                #region Fetch all required MasterData and Other data
                var tempProjects = new List<Lrb.Domain.Entities.Project>(await _newProjectRepo.ListAsync(CancellationToken.None));
                var properties = new List<Property>(await _propertyRepo.ListAsync(CancellationToken.None));
                var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(CancellationToken.None));
                var leadStatuses = new List<CustomMasterLeadStatus>(await _customMastereadStatus.ListAsync(CancellationToken.None));
                var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(CancellationToken.None));
                var existingLeads = await _leadRepo.ListAsync(new DuplicateLeadCheckSpec(), CancellationToken.None);
                var subSources = (await _dapperRepository.GetAllIntegrationSubSourceAsync<SourceDto>(_currentUser.GetTenant() ?? string.Empty)).ToList();
                var areaUnits = new List<MasterAreaUnit>(await _masterAreaUnitRepo.ListAsync(CancellationToken.None));
                List<Agency> agencies = new((await _agencyRepo.ListAsync(CancellationToken.None)).Where(i => !string.IsNullOrWhiteSpace(i.Name)));
                List<ChannelPartner> channelPartner = new((await _cpRepository.ListAsync(CancellationToken.None)).Where(i => !string.IsNullOrWhiteSpace(i.FirmName)));
                List<Campaign> campaigns = new((await _campaignRepo.ListAsync(CancellationToken.None)).Where(i => !string.IsNullOrWhiteSpace(i.Name)));
                #endregion
                #region checking For new Properties or projects
                List<Property> newProperties = new();
                List<Lrb.Domain.Entities.Project> newProjects = new();
                List<Agency> newAgencies = new();
                List<ChannelPartner> newChannels = new();
                List<Campaign> newCampaign = new();

                if (((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.Property] != null))
                    || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.Project] != null))
                    || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AgencyName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.AgencyName] != null))
                    || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ChannelPartnerName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.ChannelPartnerName] != null))
                    || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.CampaignName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.CampaignName] != null)))
                {
                    var existingPropertynames = properties.Where(i => i != null && !string.IsNullOrEmpty(i.Title)).Select(i => i.Title?.ToLower().Trim()).ToList();
                    var existingprojctnames = tempProjects.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                    var existingAgencyNames = agencies.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                    var exstingChannelPartners = channelPartner.Where(i => i != null && !string.IsNullOrEmpty(i.FirmName)).Select(i => i.FirmName?.ToLower().Trim()).ToList();
                    var exstingCampaigns = campaigns.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();

                    var isPropertyPresent = ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.Property] != null));
                    var isProjectPresent = ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.Project] != null));
                    var isAgencyNamePresent = ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AgencyName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.AgencyName] != null));
                    var isChannelPresent = ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ChannelPartnerName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.ChannelPartnerName] != null));
                    var isCampaignPresent = ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.CampaignName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.CampaignName] != null));
                    dataTable.AsEnumerable().ToList().ForEach(row =>
                    {
                        if (isPropertyPresent)
                        {
                            var propertyName = row[leadUploadTracker.MappedColumnsData[DataColumns.Property]]?.ToString();
                            if (!string.IsNullOrWhiteSpace(propertyName) && !(existingPropertynames?.Contains(propertyName.ToLower().Trim()) ?? false) && !newProperties.Select(i => i.Title).Contains(propertyName))
                            {
                                newProperties.Add(new()
                                {
                                    Title = propertyName.Trim(),
                                });
                            }
                        }
                        if (isProjectPresent)
                        {
                            var projectName = row[leadUploadTracker.MappedColumnsData[DataColumns.Project]]?.ToString();
                            if (!string.IsNullOrWhiteSpace(projectName) && !(existingprojctnames?.Contains(projectName.ToLower()) ?? false) && !newProjects.Select(i => i.Name).Contains(projectName))
                            {
                                newProjects.Add(new()
                                {
                                    Name = projectName.Trim(),
                                });
                            }
                        }
                        if (isAgencyNamePresent)
                        {
                            var agencyName = row[leadUploadTracker.MappedColumnsData[DataColumns.AgencyName]]?.ToString();
                            if (!string.IsNullOrWhiteSpace(agencyName) && !(existingAgencyNames?.Contains(agencyName.ToLower()) ?? false) && !newAgencies.Select(i => i.Name).Contains(agencyName))
                            {
                                newAgencies.Add(new()
                                {
                                    Name = agencyName,
                                    CreatedBy = leadUploadTracker.CreatedBy,
                                    LastModifiedBy = leadUploadTracker.LastModifiedBy,
                                });
                            }
                        }
                        if (isChannelPresent)
                        {
                            var cpName = row[leadUploadTracker.MappedColumnsData[DataColumns.ChannelPartnerName]]?.ToString();
                            if (!string.IsNullOrWhiteSpace(cpName) && !(exstingChannelPartners?.Contains(cpName.ToLower().Trim()) ?? false) && !newChannels.Select(i => i.FirmName).Contains(cpName))
                            {
                                newChannels.Add(new()
                                {
                                    FirmName = cpName.Trim(),
                                    CreatedBy = leadUploadTracker.CreatedBy,
                                    LastModifiedBy = leadUploadTracker.LastModifiedBy,
                                });
                            }
                        }
                        if (isCampaignPresent)
                        {
                            var campaignName = row[leadUploadTracker.MappedColumnsData[DataColumns.CampaignName]]?.ToString();
                            if (!string.IsNullOrWhiteSpace(campaignName) && !(exstingCampaigns?.Contains(campaignName.ToLower()) ?? false) && !newCampaign.Select(i => i.Name).Contains(campaignName))
                            {
                                newCampaign.Add(new()
                                {
                                    Name = campaignName,
                                    CreatedBy = leadUploadTracker.CreatedBy,
                                    LastModifiedBy = leadUploadTracker.LastModifiedBy,
                                });
                            }
                        }

                    });
                }
                if (newProperties.Any())
                {
                    await _propertyRepo.AddRangeAsync(newProperties);
                    properties.AddRange(newProperties);
                }
                if (newProjects.Any())
                {
                    await _newProjectRepo.AddRangeAsync(newProjects);
                    tempProjects.AddRange(newProjects);
                }
                if (newAgencies.Any())
                {
                    await _agencyRepo.AddRangeAsync(newAgencies);
                    agencies.AddRange(newAgencies);
                }
                if (newChannels.Any())
                {
                    await _cpRepository.AddRangeAsync(newChannels);
                    channelPartner.AddRange(newChannels);
                }
                if (newCampaign.Any())
                {
                    await _campaignRepo.AddRangeAsync(newCampaign);
                    campaigns.AddRange(newCampaign);
                }
                #endregion

                List<IntegrationInfoDto> integrationAccounts = new();
                var unMappedColumns = dataTable.GetUnmappedColumnNames(leadUploadTracker?.MappedColumnsData);
                var globalSettingInfoList = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec());

                var leads = dataTable.ConvertToLeads2(leadUploadTracker?.MappedColumnsData, unMappedColumns, tempProjects.DistinctBy(i => i.Name).ToList(), properties.DistinctBy(i => i.Title).ToList(), propetyTypes, areaUnits, leadStatuses, users, subSources, integrationAccounts, agencies, globalSettingInfoList, channelPartner, leadUploadTracker:null, campaigns);
                Console.WriteLine($"leads converted - > ConvertToLeads2()");
                leads.ForEach(lead => lead.SetLeadGoogleSheet(sheet.MappedColumnsData, _currentUser.GetUserId()));

                leads = leads.DistinctBy(i => i.ContactNo).ToList();
                var distinctLeadCount = leads.Count();
                var existingContactNos = existingLeads.Select(i => i.ContactNo).ToList();
                List<Lrb.Domain.Entities.Lead> leadsToUpdate = new();
                Parallel.ForEach(leads, lead =>
                {
                    if (string.IsNullOrWhiteSpace(lead.ContactNo) || !(lead?.ContactNo?.Length >= 10) || !Regex.IsMatch(lead.ContactNo, RegexPatterns.IndianPhoneNumberPattern))
                    {
                        var invalidLead = lead.Adapt<InvalidData>();
                        invalidLead.Errors = "Invalid ContactNo";
                        invalids.Add(invalidLead);
                    }
                    if (string.IsNullOrEmpty(lead.Name))
                    {
                        var invalidLead = lead.Adapt<InvalidData>();
                        invalidLead.Errors = "Invalid Name";
                        invalids.Add(invalidLead);
                    }
                    else if (existingContactNos.Any(i => !string.IsNullOrWhiteSpace(lead.ContactNo) && lead.ContactNo.Length >= 1 && i.Contains(lead.ContactNo)))
                    {
                        leadsToUpdate.Add(lead);
                    }
                });
                leads.RemoveAll(i => leadsToUpdate.Select(i => i.ContactNo).Contains(i.ContactNo));
                leads.RemoveAll(i => invalids.Select(i => i.ContactNo).Contains(i.ContactNo));
                //update Tracker
                leadUploadTracker.Status = UploadStatus.InProgress;
                leadUploadTracker.TotalCount = dataTable.Rows.Count;
                leadUploadTracker.DistictLeadCount = distinctLeadCount;
                Console.WriteLine($"Sheet title -> {sheet.Title}, leads count: {leads.Count}");
                Console.WriteLine($"Sheet title -> {sheet.Title}, invalids count: {invalids.Count}");
                Console.WriteLine($"Sheet title -> {sheet.Title},  leadsToUpdate count: {leadsToUpdate.Count}");
                if (invalids.Any())
                {
                    leadUploadTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty").Count();
                    byte[] bytes = CreateLeadHelper.CreateExcelData(invalids).ToArray();
                    string fileName = $"InvalidLeads-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                    string folder = "Leads";
                    var key = await _blobStorageService?.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                    //var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                    leadUploadTracker.InvalidDataS3BucketKey = key;
                }
                await _googleSheetTrackerRepo.UpdateAsync(leadUploadTracker);
                Console.WriteLine($"BulkLeadUploadTracker Updated Status: {leadUploadTracker.Status} \n {JsonConvert.SerializeObject(leadUploadTracker)}");
                try
                {
                    GoogleSheetUploadTracker? tracker = new();
                    tracker = await _googleSheetTrackerRepo.FirstOrDefaultAsync(new GoogleSheetUploadTrackerSpec(sheet.Id), CancellationToken.None);
                    if (leads.Count > 0)
                    {

                        foreach (var lead in leads)
                        {
                            var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo));
                            if (parentLead != null)
                            {
                                lead.RootId = parentLead.Id;
                                lead.DuplicateLeadVersion = "D" + (parentLead.ChildLeadsCount + 1);
                                lead.ParentLeadId = parentLead.Id;
                                parentLead.ChildLeadsCount += 1;
                                try
                                {
                                    await _leadRepo.UpdateAsync(parentLead);
                                }
                                catch (Exception e)
                                {
                                    Console.WriteLine("parent lead update  -> Exception : " + JsonConvert.SerializeObject(e));
                                }
                            }
                        }
                        await _leadRepo.AddRangeAsync(leads);
                        Console.WriteLine("leads added -> AddRangeAsync()");
                        var leadDtos = leads.Adapt<List<ViewLeadDto>>();
                        List<LeadHistory> leadHistories = new();
                        leadDtos.ForEach(leadDto =>
                        {
                            leadDto.SetUsersInViewLeadDto(users);
                            leadHistories.Add(LeadHistoryHelper.LeadHistoryMapper(leadDto));
                        });
                        leadHistories = (await _leadHistoryRepo.AddRangeAsync(leadHistories)).ToList();
                        if (tracker != null)
                        {
                            tracker.TotalUploadedCount += leads.Count;
                        }

                    }
                    if (leadsToUpdate?.Any() ?? false)
                    {
                        List<Lrb.Domain.Entities.Lead> uLeads = new();
                        for (int i = 0; i < leadsToUpdate.Count; i++)
                        {
                            try
                            {
                                var duplicateLeadsToUpdate = await _leadRepo.ListAsync(new GetLeadByContactNoSpec(leadsToUpdate[i].ContactNo ?? string.Empty));
                                var updatedLeads = MapObjects(leadsToUpdate[i], duplicateLeadsToUpdate, tempProjects, properties);
                                uLeads.AddRange(updatedLeads);
                            }
                            catch (Exception e)
                            {
                                Console.WriteLine("leadsToUpdate -> Exception: " + JsonConvert.SerializeObject(e));
                            }

                        }

                        await _leadRepo.UpdateRangeAsync(uLeads);
                        Console.WriteLine("leads updated -> UpdateRangeAsync()");
                        var leadDtos = leadsToUpdate.Adapt<List<ViewLeadDto>>();
                        List<LeadHistory> leadHistories = new();
                        leadDtos.ForEach(leadDto =>
                        {
                            leadDto.SetUsersInViewLeadDtoAsync(_userService, CancellationToken.None);
                            leadHistories.Add(LeadHistoryHelper.LeadHistoryMapper(leadDto));
                        });
                        if (leadHistories.Any())
                        {
                            try
                            {
                                await UpdateDuplicateLeadsHistoryAsync(leadHistories);

                            }
                            catch (Exception e)
                            {
                                Console.WriteLine($"UpdateDuplicateLeadsHistoryAsync() -> Exception :{JsonConvert.SerializeObject(e)}");
                            }


                        }
                        leadUploadTracker.LeadsUpdatedCount = leadsToUpdate.Count();
                        leadUploadTracker.Status = UploadStatus.Completed;
                        await _googleSheetTrackerRepo.UpdateAsync(leadUploadTracker);
                    }
                }
                catch (Exception e)
                {
                    leadUploadTracker.Status = UploadStatus.Failed;
                    await _googleSheetTrackerRepo.UpdateAsync(leadUploadTracker);
                    throw;
                }
                return true;
            }
            return false;
        }

    }
}

