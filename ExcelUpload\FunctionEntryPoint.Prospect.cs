﻿using Amazon.DynamoDBv2.Model;
using Amazon.S3.Model;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.DataManagement;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Collections.Concurrent;
using System.Data;
using System.Text.RegularExpressions;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ProspectHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            BulkProspectUploadTracker? bulkProspectUpload = (await _bulkProspectUploadRepo.ListAsync(new GetBulkProspectByTrackerId(input.TrackerId))).FirstOrDefault();
            try
            {
                if (bulkProspectUpload != null)
                {
                    try
                    {
                        bulkProspectUpload.MappedColumnData = bulkProspectUpload.MappedColumnData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        bulkProspectUpload.Status = Lrb.Domain.Enums.UploadStatus.Started;
                        bulkProspectUpload.LastModifiedBy = input.CurrentUserId;
                        bulkProspectUpload.CreatedBy = input.CurrentUserId;
                        var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                        await _bulkProspectUploadRepo.UpdateAsync(bulkProspectUpload);
                        Console.WriteLine($"handler() -> ProspectHandler Updated Status: {bulkProspectUpload.Status} \n {JsonConvert.SerializeObject(bulkProspectUpload)}");

                        #region fetch all required data
                        var properties = new List<Property>(await _propertyRepo.ListAsync(cancellationToken));
                        var existingProspects = await _prospectRepo.ListAsync(new GetAllBulkProspectsSpecs(), cancellationToken);
                        var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                        var prospectStatus = await _prospectStatusRepo.ListAsync(cancellationToken);
                        var sources = await _prospectSourceRepo.ListAsync(cancellationToken);
                        var projects = new List<Project>(await _newProjectRepo.ListAsync(cancellationToken));
                        List<Agency> agencies = new((await _agencyRepo.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.Name)));
                        List<ChannelPartner> channelPartner = new((await _cpRepository.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.FirmName)));
                        List<Campaign> campaigns = new((await _campaignRepo.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.Name)));

                        #endregion

                        #region Convert To DataTable
                        Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", bulkProspectUpload.S3BucketKey);
                        DataTable dataTable = new();
                        if (bulkProspectUpload.S3BucketKey.Split('.').LastOrDefault() == "csv")
                        {
                            using MemoryStream memoryStream = new();
                            fileStream.CopyTo(memoryStream);
                            dataTable = CSVHelper.CSVToDataTable(memoryStream);
                        }
                        else
                        {
                            dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, bulkProspectUpload.SheetName);
                        }

                        List<InvalidProspect> invalids = new();
                        int totalRows = dataTable.Rows.Count;
                        for (int i = totalRows - 1; i >= 0; i--)
                        {
                            var row = dataTable.Rows[i];
                            var data1 = row[bulkProspectUpload.MappedColumnData[ProspectDataColumn.Name]].ToString();
                            var data2 = row[bulkProspectUpload.MappedColumnData[ProspectDataColumn.ContactNo]].ToString();
                            if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                            {
                                row.Delete();
                            }
                            else if (string.IsNullOrEmpty(row[bulkProspectUpload.MappedColumnData[ProspectDataColumn.Name]].ToString()) && string.IsNullOrEmpty(row[bulkProspectUpload.MappedColumnData[ProspectDataColumn.ContactNo]].ToString()))
                            {
                                var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i.ToString())));
                                var invalidData = new InvalidProspect
                                {
                                    Errors = "contact number and name are empty",
                                    Notes = notes


                                };
                                if (!invalids.Any(i => i.Notes == invalidData.Notes))
                                {
                                    invalids.Add(invalidData);
                                }
                                row.Delete();
                            }
                        }
                        if (dataTable.Rows.Count <= 0)
                        {
                            throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                        }
                        totalRows = dataTable.Rows.Count;
                        Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        #endregion

                        #region Checking For new Prosperties and Project and Agencies

                        List<Property> newProperties = new();
                        List<Lrb.Domain.Entities.Project> newProjects = new();
                        List<Agency> newAgencies = new();
                        List<ChannelPartner> newChannels = new();
                        List<Campaign> newCampaign = new();
                        if ((bulkProspectUpload.MappedColumnData?.ContainsKey(ProspectDataColumn.Property) ?? false && bulkProspectUpload?.MappedColumnData[ProspectDataColumn.Property] != null)
                            || (bulkProspectUpload.MappedColumnData?.ContainsKey(ProspectDataColumn.Project) ?? false && bulkProspectUpload?.MappedColumnData[ProspectDataColumn.Project] != null)
                            || (bulkProspectUpload.MappedColumnData?.ContainsKey(ProspectDataColumn.AgencyName) ?? false && bulkProspectUpload?.MappedColumnData[ProspectDataColumn.AgencyName] != null)
                            || (bulkProspectUpload.MappedColumnData?.ContainsKey(ProspectDataColumn.ChannelPartnerName) ?? false && bulkProspectUpload?.MappedColumnData[ProspectDataColumn.ChannelPartnerName] != null)
                            || (bulkProspectUpload.MappedColumnData?.ContainsKey(ProspectDataColumn.CampaignName) ?? false && bulkProspectUpload?.MappedColumnData[ProspectDataColumn.CampaignName] != null))

                        {
                            var existingPropertynames = properties?.Where(i => i != null && !string.IsNullOrEmpty(i.Title)).Select(i => i.Title?.ToLower().Trim()).ToList();
                            var existingProjectNames = projects?.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                            var existingAgencyNames = agencies?.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                            var exstingChannelPartners = channelPartner.Where(i => i != null && !string.IsNullOrEmpty(i.FirmName)).Select(i => i.FirmName?.ToLower().Trim()).ToList();
                            var exstingCampaigns = campaigns.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                            var isPropertyPresent = ((bulkProspectUpload.MappedColumnData?.ContainsKey(ProspectDataColumn.Property) ?? false) && (bulkProspectUpload.MappedColumnData[ProspectDataColumn.Property] != null));
                            var isProjectPresent = ((bulkProspectUpload.MappedColumnData?.ContainsKey(ProspectDataColumn.Project) ?? false) && (bulkProspectUpload.MappedColumnData[ProspectDataColumn.Project] != null));
                            var isAgencyNamePresent = ((bulkProspectUpload.MappedColumnData?.ContainsKey(ProspectDataColumn.AgencyName) ?? false) && (bulkProspectUpload.MappedColumnData[ProspectDataColumn.AgencyName] != null));
                            var isChannelPresent = ((bulkProspectUpload.MappedColumnData?.ContainsKey(ProspectDataColumn.ChannelPartnerName) ?? false) && (bulkProspectUpload.MappedColumnData[ProspectDataColumn.ChannelPartnerName] != null));
                            var isCampaignPresent = ((bulkProspectUpload.MappedColumnData?.ContainsKey(ProspectDataColumn.CampaignName) ?? false) && (bulkProspectUpload.MappedColumnData[ProspectDataColumn.CampaignName] != null));

                            dataTable.AsEnumerable().ToList().ForEach(row =>
                            {
                                if (isPropertyPresent)
                                {
                                    var propertyName = row[bulkProspectUpload.MappedColumnData[ProspectDataColumn.Property]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(propertyName) && !(existingPropertynames?.Contains(propertyName.ToLower().Trim()) ?? false) && !newProperties.Select(i => i.Title).Contains(propertyName))
                                    {
                                        newProperties.Add(new()
                                        {
                                            Title = propertyName.Trim(),
                                        });
                                    }
                                }
                                if (isProjectPresent)
                                {
                                    var projectName = row[bulkProspectUpload.MappedColumnData[ProspectDataColumn.Project]]?.ToString();
                                    if (!string.IsNullOrEmpty(projectName) && !(existingProjectNames?.Contains(projectName.ToLower().Trim()) ?? false) && !newProjects.Select(i => i.Name).Contains(projectName))
                                    {
                                        newProjects.Add(new()
                                        {
                                            Name = projectName.Trim()
                                        });
                                    }
                                }
                                if (isAgencyNamePresent)
                                {
                                    var agencyName = row[bulkProspectUpload.MappedColumnData[ProspectDataColumn.AgencyName]]?.ToString();
                                    if (!string.IsNullOrEmpty(agencyName) && !(existingAgencyNames?.Contains(agencyName.ToLower().Trim()) ?? false) && !newAgencies.Select(i => i.Name).Contains(agencyName))
                                    {
                                        newAgencies.Add(new()
                                        {
                                            Name = agencyName.Trim(),
                                            CreatedBy = bulkProspectUpload.CreatedBy,
                                            LastModifiedBy = bulkProspectUpload.LastModifiedBy,

                                        });
                                    }
                                }
                                if (isChannelPresent)
                                {
                                    var cpName = row[bulkProspectUpload.MappedColumnData[ProspectDataColumn.ChannelPartnerName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(cpName) && !(exstingChannelPartners?.Contains(cpName.ToLower().Trim()) ?? false) && !newChannels.Select(i => i.FirmName).Contains(cpName))
                                    {
                                        newChannels.Add(new()
                                        {
                                            FirmName = cpName.Trim(),
                                            CreatedBy = bulkProspectUpload.CreatedBy,
                                            LastModifiedBy = bulkProspectUpload.LastModifiedBy,
                                        });
                                    }
                                }
                                if (isCampaignPresent)
                                {
                                    var campaignName = row[bulkProspectUpload.MappedColumnData[ProspectDataColumn.CampaignName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(campaignName) && !(exstingCampaigns?.Contains(campaignName.ToLower()) ?? false) && !newCampaign.Select(i => i.Name).Contains(campaignName))
                                    {
                                        newCampaign.Add(new()
                                        {
                                            Name = campaignName,
                                            CreatedBy = bulkProspectUpload.CreatedBy,
                                            LastModifiedBy = bulkProspectUpload.LastModifiedBy,
                                        });
                                    }
                                }

                            });
                        }
                        if (newProperties.Any())
                        {
                            await _propertyRepo.AddRangeAsync(newProperties);
                            properties.AddRange(newProperties);
                        }
                        if (newProjects.Any())
                        {
                            await _newProjectRepo.AddRangeAsync(newProjects);
                            projects.AddRange(newProjects);
                        }
                        if (newAgencies.Any())
                        {
                            await _agencyRepo.AddRangeAsync(newAgencies);
                            agencies.AddRange(newAgencies);
                        }
                        if (newChannels.Any())
                        {
                            await _cpRepository.AddRangeAsync(newChannels);
                            channelPartner.AddRange(newChannels);
                        }
                        if (newCampaign.Any())
                        {
                            await _campaignRepo.AddRangeAsync(newCampaign);
                            campaigns.AddRange(newCampaign);
                        }
                        #endregion

                        var unMappedColumn = dataTable.GetUnmappedProspectColumnNames(bulkProspectUpload?.MappedColumnData);
                        var globalSettingInfoList = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);

                        var prospects = dataTable.ConvertToProspect(bulkProspectUpload.MappedColumnData, unMappedColumn, propetyTypes, properties.DistinctBy(i => i.Title).ToList(), prospectStatus, sources, projects, agencies, globalSettingInfoList, channelPartner, bulkProspectUpload, campaigns);

                        prospects = prospects.DistinctBy(i => i.ContactNo).ToList();



                        foreach (var prospect in prospects)
                        {
                            if (!string.IsNullOrWhiteSpace(prospect.ContactNo))
                            {
                                var contactNo = BulkUploadHelper.ConcatenatePhoneNumber(prospect.CountryCode, prospect.ContactNo, globalSettingInfoList);
                                if (string.IsNullOrWhiteSpace(contactNo))
                                {
                                    var invalidProspect = prospect.Adapt<InvalidProspect>();
                                    invalidProspect.Errors = "Invalid ContactNo";
                                    invalids.Add(invalidProspect);
                                }
                                else
                                {
                                    prospect.ContactNo = contactNo;
                                    prospect.ContactNo = contactNo;
                                    prospect.CreatedBy = input.CurrentUserId;
                                    prospect.LastModifiedBy = input.CurrentUserId;
                                    prospect.AssignTo = input.CurrentUserId;
                                    prospect.AssignedFrom = input.CurrentUserId;
                                    if (!string.IsNullOrWhiteSpace(prospect.AlternateContactNo))
                                    {
                                        var altcontactno = BulkUploadHelper.ConcatenatePhoneNumber(prospect.CountryCode, prospect.AlternateContactNo, globalSettingInfoList);
                                        prospect.AlternateContactNo = altcontactno;
                                    }
                                }
                            }
                            else
                            {
                                var invalidProspect = prospect.Adapt<InvalidProspect>();
                                invalidProspect.Errors = "Invalid ContactNo";
                                invalids.Add(invalidProspect);
                            }
                        }
                        var distinctCount = prospects.Count();
                        Console.WriteLine($"handler() -> Total Distinct Prospect: {distinctCount}");
                        var existingContactNos = existingProspects.SelectMany(i => new[] { i.ContactNo, i.AlternateContactNo })
                            .Where(i => !string.IsNullOrWhiteSpace(i)).ToList();

                        List<Prospect> prospectToUpdate = new();
                        foreach (Prospect prospect in prospects)
                        {
                            if (existingContactNos.Any(i => !string.IsNullOrWhiteSpace(prospect.ContactNo)))
                            {
                                var invalidProspect = prospect.Adapt<InvalidProspect>();

                                invalidProspect.Errors = "Duplicate Data";
                                var duplicateProspect = existingProspects.FirstOrDefault(i => (!string.IsNullOrWhiteSpace(prospect.ContactNo) && ((i.ContactNo?.Contains(prospect.ContactNo) == true) || (i.AlternateContactNo?.Contains(prospect.ContactNo) == true))) 
                                || (!string.IsNullOrWhiteSpace(prospect.AlternateContactNo) && ((i.ContactNo?.Contains(prospect.AlternateContactNo) == true) || (i.AlternateContactNo?.Contains(prospect.AlternateContactNo) == true))));

                                if (duplicateProspect != null)
                                {
                                    var user = users.FirstOrDefault(i => i.Id == duplicateProspect.AssignTo);
                                    invalidProspect.AssignTo = user != null ? user.FirstName + " " + user.LastName : string.Empty;
                                    invalidProspect.Source = duplicateProspect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Source?.DisplayName?.ToString() ?? duplicateProspect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                                    invalidProspect.SubSource = duplicateProspect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? duplicateProspect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                                    invalidProspect.Status = duplicateProspect.Status?.DisplayName ?? string.Empty;
                                    invalidProspect.Created = duplicateProspect.CreatedOn.Date;
                                    invalids.Add(invalidProspect);
                                }
                            }
                        }
                        prospects.RemoveAll(i => prospectToUpdate.Select(i => i.ContactNo).Contains(i.ContactNo));
                        prospects.RemoveAll(i => invalids.Select(i => i.ContactNo).Contains(i.ContactNo));


                        bulkProspectUpload.Status = UploadStatus.InProgress;
                        bulkProspectUpload.TotalCount = totalRows;
                        bulkProspectUpload.DistinctProspectCount = distinctCount;
                        bulkProspectUpload.LastModifiedBy = input.CurrentUserId;
                        bulkProspectUpload.CreatedBy = input.CurrentUserId;
                        if (invalids.Any())
                        {
                            bulkProspectUpload.DuplicateCount = invalids.Where(i => i.Errors == "Duplicate Data").Count();
                            bulkProspectUpload.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty").Count();
                            bulkProspectUpload.ProspectCount = prospectToUpdate.Count();
                            byte[] bytes = ProspectHelper.CreateExcelData(invalids).ToArray();
                            string fileName = $"InvalidProspect-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = "Prospects";
                            var key = await _blobStorageService?.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            bulkProspectUpload.InvalidDataS3BucketKey = key;
                        }
                        await _bulkProspectUploadRepo.UpdateAsync(bulkProspectUpload);
                        Console.WriteLine($"handler() -> BulkProspectUploadTracker Updated Status: {bulkProspectUpload.Status} \n {JsonConvert.SerializeObject(bulkProspectUpload)}");
                        BulkUploadBackgroundDto backgroundDto = new();
                        if (prospects.Count > 0)
                        {
                            int prospectsPerchunk = prospects.Count > 5000 ? 5000 : prospects.Count;
                            var chunks = prospects.Chunk(prospectsPerchunk).Select(i => new ConcurrentBag<Prospect>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkUploadBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = bulkProspectUpload.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Prospects = new(chunk),
                                    UserIds = new(bulkProspectUpload.UserIds ?? new()),
                                    Users = users.ToList()
                                };
                                await ExecuteDBOperationsAsync(backgroundDto);
                                chunkIndex++;
                            }
                        }
                        bulkProspectUpload.Status = UploadStatus.Completed;
                        bulkProspectUpload.LastModifiedBy = input.CurrentUserId;
                        bulkProspectUpload.CreatedBy = input.CurrentUserId;
                        await _bulkProspectUploadRepo.UpdateAsync(bulkProspectUpload);
                    }

                    catch (Exception ex)
                    {
                        bulkProspectUpload = await _bulkProspectUploadRepo.GetByIdAsync(bulkProspectUpload.Id);
                        bulkProspectUpload.Status = UploadStatus.Failed;
                        bulkProspectUpload.Message = ex.Message;
                        bulkProspectUpload.LastModifiedBy = input.CurrentUserId;
                        bulkProspectUpload.CreatedBy = input.CurrentUserId;
                        await _bulkProspectUploadRepo.UpdateAsync(bulkProspectUpload);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkProspectleadUploadTrackerUsingEPPlus -> ProspectHandler()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkProspectleadUploadTrackerUsingEPPlus -> ProspectHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
        }

        public async Task ExecuteDBOperationsAsync(BulkUploadBackgroundDto dto)
        {
            BulkChannelPartnerUploadTracker bulkChannelPartner = new();
            var tracker = (await _bulkProspectUploadRepo.ListAsync(new GetBulkProspectByTrackerId(dto.TrackerId))).FirstOrDefault();
            try
            {
                try
                {
                    if (dto.UserIds?.Any() ?? false)
                    {
                        var users = await _userDetailsRepo.ListAsync(new GetUsersSpec(dto.UserIds.Select(i => Guid.Parse(i)).ToList() ?? new()));
                        dto?.Prospects?.AssignProspect(users.Where(i => i.IsAutomationEnabled).Select(i => i.UserId).ToList(), dto.CurrentUserId);
                    }
                    var prospectdeatils = await _prospectRepo.AddRangeAsync(dto.Prospects);
                    var prospectVM = prospectdeatils.Adapt<List<Lrb.Application.DataManagement.Web.Dtos.ViewProspectDto>>();
                    var sources = await _prospectSourceRepo.ListAsync();
                    var statuses = await _prospectStatusRepo.ListAsync();
                    var propertyTypes = await _propertyTypeRepo.ListAsync();
                    List<ProspectHistory> allHistories = new();

                    foreach (var vm in prospectVM)
                    {
                        await ProspectHistoryHelper.SetUserViewForProspect(vm, _userService, dto.CancellationToken, currentUserId: dto.CurrentUserId);
                        var histories = await ProspectHistoryHelper.CreateProspectHistoryForVM(vm, null, dto.CurrentUserId, 1, statuses, propertyTypes, sources, _userService, dto.CancellationToken);
                        allHistories.AddRange(histories);
                    }

                    await _prospectHistoryRepo.AddRangeAsync(allHistories);
                }
                catch (Exception e)
                {

                }
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Prospects.Count;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkProspectUploadRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == tracker.InvalidCount)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkProspectUploadRepo.UpdateAsync(tracker);
            }
        }

    }
}




