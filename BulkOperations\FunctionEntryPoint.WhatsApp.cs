﻿using Lrb.Application.Campaigns.Spec;
using Lrb.Application.Common.WhatsApp.Interakt;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.CommonHandler;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.PushNotification.Web.Dtos;
using Lrb.Application.Reports.Web;
using Lrb.Application.WA.Web;
using Lrb.Application.WhatsAppCloudApi.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Auth;
using Mapster;
using Microsoft.Graph;
using Newtonsoft.Json;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task SendWhatsAppBulkTemplateHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _whatsAppBulkTemplateTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            SendWhatsAppTemplateInBulkRequest? request = JsonConvert.DeserializeObject<SendWhatsAppTemplateInBulkRequest>(tracker?.Request ?? string.Empty);
            WhatsAppTemplateInfo? whatsAppTemplateInfo = await _whatsAppTemplateInfoRepo.FirstOrDefaultAsync(new GetWhatsAppTemplateInfoByNameSpec(request.CodeName ?? string.Empty), cancellationToken);
            if (whatsAppTemplateInfo == null)
            {
                var waTemplate = await _watemplateRepo.FirstOrDefaultAsync(new GetAllWATemplatesSpec(request.CodeName), cancellationToken);
                if (waTemplate != null)
                {
                    var leadIds = request.LeadContactInfoDtos?.Select(d => d.Id ?? Guid.Empty).ToList();
                    Campaign? campaign = new();
                    if (!string.IsNullOrWhiteSpace(request?.CampaignName))
                    {
                        var campaignName = request.CampaignName.Trim();
                        campaign = await _campaignRepo.FirstOrDefaultAsync(new GetCampaignByNameSpec(campaignName), cancellationToken);
                        if (campaign == null)
                        {
                            campaign = new Campaign
                            {
                                Name = campaignName,
                                ContactTypes = new List<ContactType> { ContactType.WhatsApp },
                                TemplateNames = new List<string> { waTemplate.Name }
                            };
                            campaign = await _campaignRepo.AddAsync(campaign, cancellationToken);
                        }
                        else
                        {
                            campaign.ContactTypes ??= new List<ContactType>();
                            if (!campaign.ContactTypes.Contains(ContactType.WhatsApp))
                            {
                                campaign.ContactTypes.Add(ContactType.WhatsApp);
                            }
                            campaign.TemplateNames ??= new List<string>();
                            if (!string.IsNullOrWhiteSpace(waTemplate.Name) && !campaign.TemplateNames.Contains(waTemplate.Name))
                            {
                                campaign.TemplateNames.Add(waTemplate.Name);
                            }
                            await _campaignRepo.UpdateAsync(campaign, cancellationToken);
                        }
                        if (request.IsData == true)
                        {
                            var prospects = await _prospectRepo.ListAsync(new ProspectForCampaignSpec(leadIds ?? new()), cancellationToken);
                            if (prospects?.Any() ?? false)
                            {
                                var updatedProspects = new List<Prospect>();
                                var prospectHistories = new List<ProspectHistory>();

                                var statuses = await _prospectStatusRepo.ListAsync(cancellationToken);
                                var propertyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
                                var sources = await _prospectSourceRepo.ListAsync(cancellationToken);

                                var userIds = new List<string?>();
                                prospects.ForEach(i =>
                                {
                                    userIds.AddRange(new[] { i.AssignedFrom?.ToString(), i.AssignTo.ToString(),
                                 i.LastModifiedBy.ToString(), i.SourcingManager?.ToString(),
                                 i.ClosingManager?.ToString() });
                                });
                                userIds.Add(input.CurrentUserId.ToString());
                                userIds = userIds.Where(i => !string.IsNullOrEmpty(i))?.DistinctBy(i => i).ToList();
                                var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);

                                foreach (var prospect in prospects)
                                {
                                    if (prospect.Campaigns?.Any(c => c.Id == campaign.Id) ?? false)
                                        continue;
                                    var oldProspect = prospect.Adapt<ViewProspectDto>();
                                    oldProspect = await Lrb.Application.DataManagement.Web.Mapping.ProspectHistoryHelper.SetUserViewForProspect(oldProspect, _userService, cancellationToken, currentUserId: input.CurrentUserId, userDetails: users);

                                    prospect.Campaigns ??= new List<Campaign>();
                                    prospect.Campaigns.Add(campaign);
                                    prospect.LastModifiedBy = input.CurrentUserId;
                                    prospect.LastModifiedOn = DateTime.UtcNow;
                                    updatedProspects.Add(prospect);

                                    // Create prospect history
                                    var prospectVM = prospect.Adapt<ViewProspectDto>();
                                    prospectVM = await Lrb.Application.DataManagement.Web.Mapping.ProspectHistoryHelper.SetUserViewForProspect(prospectVM, _userService, cancellationToken, currentUserId: input.CurrentUserId, userDetails: users);
                                    var histories = await Lrb.Application.DataManagement.Web.Mapping.ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, input.CurrentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);
                                    prospectHistories.AddRange(histories);
                                }
                                if (updatedProspects.Any())
                                {
                                    await _prospectRepo.UpdateRangeAsync(updatedProspects);
                                    await _prospectHistoryRepo.AddRangeAsync(prospectHistories);
                                }
                            }
                        }
                        else
                        {
                            var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");
                            var leads = await _leadRepo.ListAsync(new LeadsByIdsSpec(leadIds), cancellationToken);
                            if (leads?.Any() ?? false)
                            {
                                var leadsToUpdate = new List<Lead>();
                                List<LeadHistory> updateLeadHistories = new();
                                List<LeadHistory> insertLeadHistories = new();
                                var oldLeadHistorys = await _leadHistoryRepo.ListAsync(new LeadHistorySpec(leads.Select(i => i.Id).ToList()));
                                foreach (var lead in leads)
                                {
                                    // Check if the lead is already part of the campaign
                                    if (lead.Campaigns == null || !lead.Campaigns.Any(c => c.Id == campaign.Id))
                                    {
                                        lead.Campaigns ??= new List<Campaign>();
                                        lead.Campaigns.Add(campaign);
                                        lead.LastModifiedBy = input.CurrentUserId;
                                        lead.LastModifiedOn = DateTime.UtcNow;
                                        leadsToUpdate.Add(lead);
                                        var leadDto = lead.Adapt<ViewLeadDto>();
                                        await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, input.CurrentUserId);
                                        var newLeadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                                        var oldLeadHistory = oldLeadHistorys.FirstOrDefault(i => i.LeadId == leadDto.Id && i.UserId == input.CurrentUserId);
                                        if (oldLeadHistory != null)
                                        {
                                            var leadHistory = LeadHistoryHelper.GetUpdatedLeadHistory(oldLeadHistory, newLeadHistory);
                                            updateLeadHistories.Add(leadHistory);
                                        }
                                        else
                                        {
                                            newLeadHistory.Id = Guid.NewGuid();
                                            newLeadHistory.CreatedDate = DateTime.UtcNow;
                                            newLeadHistory.CreatedBy = input.CurrentUserId;
                                            newLeadHistory.IsDeleted = false;
                                            newLeadHistory.UserId = input.CurrentUserId;
                                            insertLeadHistories.Add(newLeadHistory);
                                        }
                                    }
                                }

                                if (leadsToUpdate.Any())
                                {
                                    await _leadRepo.UpdateRangeAsync(leadsToUpdate, cancellationToken);
                                }
                                if (updateLeadHistories?.Any() ?? false)
                                {
                                    var leadhistories = await UpdateLeadHistoryInDapperAsync(updateLeadHistories, connection, input.TenantId ?? string.Empty);
                                }
                                if (insertLeadHistories?.Any() ?? false)
                                {
                                    await CreateLeadHistoryInDapperAsync(insertLeadHistories, connection, input.TenantId ?? string.Empty);
                                }
                            }
                        }
                    }

                    NotificationInfoDto notificationConfig = new NotificationInfoDto()
                    {
                        TemplateId = waTemplate.Id,
                        LeadIds = leadIds,
                        TemplateMode = TemplateMode.WhatsApp,
                        IsLeadSpecific = true
                    };
                    var contactNo = request.LeadContactInfoDtos?.FirstOrDefault()?.ContactNo;
                    var headerValue = request?.HeaderValue;
                    var bodyValues = request?.BodyValues;

                    var result = await _templateNotificationService.ProcessTemplateNotificationAsync(
                        notificationConfig, cancellationToken, contactNo, headerValue, bodyValues, campaign.Name
                    );
                    Console.WriteLine("Message Sent Successfully!");
                    return;
                }
            }
            if (whatsAppTemplateInfo == null)
            {
                Console.WriteLine("No Templates found by the given Name");
                return;
            }
            CommonWhatsAppTemplateDto templateDto = new();
            templateDto.Name = whatsAppTemplateInfo.CodeName;
            templateDto.LanguageCode = whatsAppTemplateInfo.LanguageCode.ToString();
            switch (whatsAppTemplateInfo.WhatsAppHeaderTypes)
            {
                case WhatsAppHeaderTypes.None:
                    break;
                case WhatsAppHeaderTypes.Text:
                    templateDto.HeaderValues = request.HeaderValue == null ? null : new List<string>() { request.HeaderValue };
                    break;
                case WhatsAppHeaderTypes.Document:
                case WhatsAppHeaderTypes.Image:
                case WhatsAppHeaderTypes.Video:
                    templateDto.HeaderValues = new List<string>() { whatsAppTemplateInfo.MediaUrl ?? string.Empty };
                    break;
                case WhatsAppHeaderTypes.DynamicCTA:
                    break;
            }
            if (whatsAppTemplateInfo.BodyValuesCount > 0 && (request.BodyValues?.Any() ?? false))
            {
                request.BodyValues.ForEach(i =>
                {
                    if (string.IsNullOrEmpty(i))
                    {
                        i = string.Empty;
                    }
                });
                templateDto.BodyValues = request.BodyValues;
            }
            else
            {
                templateDto.BodyValues = new();
            }
            if (whatsAppTemplateInfo.WhatsAppHeaderTypes == WhatsAppHeaderTypes.Document)
            {
                templateDto.FileName = request.FileName ?? string.Empty;
            }
            templateDto.ButtonValues = request.ButtonValues;
            List<BaseWhatsAppTemplateWithLeadIdDto> baseTemplateWithLeadIdDtos = new();
            if (request.LeadContactInfoDtos?.Any() ?? false)
            {
                foreach (var dto in request.LeadContactInfoDtos)
                {
                    BaseWhatsAppTemplateWithLeadIdDto baseDto = new()
                    {
                        LeadId = dto.Id,
                        Template = templateDto,
                        CallbackData = request.CallbackData,
                        FullPhoneNumber = dto.ContactNo,
                        LeadName = dto.Name,
                        ShouldAddLeadNameInBody = request.ShouldAddLeadNameInBody,
                        ShouldAddLeadNameInHeader = request.ShouldAddLeadNameInHeader,
                        CurrentUserId = input.CurrentUserId
                    };
                    baseTemplateWithLeadIdDtos.Add(baseDto);
                }
            }
            else
            {
                Console.WriteLine("Please provide Lead Contact Information.");
                return;
            }
            try
            {
                var response = await _whatsAppSenderService.SendCommonTemplateAsync(baseTemplateWithLeadIdDtos, whatsAppTemplateInfo.WhatsAppHeaderTypes, request.IsTestMessage);
                Console.WriteLine("Message Sent Successfully!");
                return;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Message wat not Sent!");
                return;
            }
        }
    }
}
