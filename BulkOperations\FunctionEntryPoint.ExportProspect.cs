﻿using Lrb.Application.Common.Exceptions;
using Lrb.Application.DataManagement.Web.Export.Dtos;
using Lrb.Application.DataManagement.Web.Export.Mapping;
using Lrb.Application.DataManagement.Web.Export.Requests;
using Lrb.Application.DataManagement.Web.Request;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Mapster;
using Microsoft.Graph;
using Newtonsoft.Json;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ExportProspectsHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var globalSettings = (await _globalSettingsRepository.ListAsync(cancellationToken)).FirstOrDefault();
            if (globalSettings != null && globalSettings.IsExportDataEnabled)
            {
                var prospectExportTracker = await _exportProspectTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
                if (prospectExportTracker == null)
                {
                    throw new NotFoundException("No Tracker found By This Id");
                }
                var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
                var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
                var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ExportLead), CancellationToken.None)).FirstOrDefault();
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                bool isSent = false;
                try
                {
                    if (prospectExportTracker != null && serviceProvider != null && exportEmailTemplate != null)
                    {
                        #region Fetch All Master Data and Other Data
                        var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                        var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                        var customProspectStatuses = new List<CustomProspectStatus>(await _prospectStatusRepo.ListAsync(cancellationToken));
                        var prospectSources = new List<MasterProspectSource>(await _prospectSourceRepo.ListAsync(cancellationToken));
                        #endregion

                        GetAllProspectRequest? request = JsonConvert.DeserializeObject<GetAllProspectRequest>(prospectExportTracker?.Request ?? string.Empty);
                        RunAWSBatchForExportProspectRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForExportProspectRequest>(prospectExportTracker?.Request ?? string.Empty);
                        List<Guid> subIds = new();
                        request.PageNumber = 1;
                        try
                        {
                            if (request?.AssignTo?.Any() ?? false)
                            {
                                if (request?.IsWithTeam ?? false)
                                {
                                    subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, input.TenantId ?? string.Empty)).ToList();
                                }
                                else
                                {
                                    subIds = request?.AssignTo ?? new List<Guid>();
                                }
                            }
                            else
                            {
                                subIds = (await _dapperRepository.GetSubordinateIdsAsync(input.CurrentUserId, input.TenantId ?? string.Empty, viewAllLeads:null))?.ToList() ?? new();
                            }
                        }
                        catch (Exception e)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                ErrorSource = e?.Source,
                                StackTrace = e?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "FunctionEntryPoint -> ExportProspectsHandler()",
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                            throw;
                        }
                        var prospects = (await _prospectRepository.GetAllProspectForWeb(request, input.CurrentUserId, subIds, customProspectStatuses)).ToList();
                        var prospectDto = ExportProspectHelper.ConvertToExportProspectDto(prospects, users, customProspectStatuses, prospectSources, propetyTypes);
                        var exportTemplate = await _exportTemplateRepo.GetByIdAsync(prospectExportTracker?.TemplateId ?? Guid.Empty);
                        var fileBytes = ExcelHelper.CreateExcelFromList(prospectDto, exportTemplate?.Properties ?? new List<string>(), new List<string> { "Id", "AssignTo" }, requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset).ToArray();
                        var fileName = ExportProspectHelper.ConvertToString(request.ProspectVisiblity) + "-" + (request.FilterType.ToString() == "AllWithNID" ? "All" : request.FilterType.ToString() == "All" ? "Active" : request.FilterType.ToString());
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(requestforFileName.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Prospects/{input.TenantId ?? "Default"}", $"Export_Prospects_" + input.TenantId + requestforFileName.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes, 0);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        List<string> toEmails = new();
                        List<string> ccEamils = new();
                        List<string> bccEamils = new();
                        if (prospectExportTracker?.ToRecipients?.Any() ?? false)
                        {
                            toEmails.AddRange(prospectExportTracker.ToRecipients);
                        }
                        if (prospectExportTracker?.CcRecipients?.Any() ?? false)
                        {
                            ccEamils.AddRange(prospectExportTracker.CcRecipients);
                        }
                        if (prospectExportTracker?.BccRecipients?.Any() ?? false)
                        {
                            bccEamils.AddRange(prospectExportTracker.BccRecipients);
                        }
                        var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                        emailSenderDto.To = toEmails;
                        emailSenderDto.Cc = ccEamils;
                        emailSenderDto.Bcc = bccEamils;
                        emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                        emailSenderDto.EmailBody = template;
                        emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                        emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                        await _graphEmailService.SendEmail(emailSenderDto);
                        isSent = true;
                        prospectExportTracker.Count = prospects.Count();
                        prospectExportTracker.S3BucketKey = presignedUrl;
                        prospectExportTracker.Template = JsonConvert.SerializeObject(exportTemplate);
                        prospectExportTracker.FileName = $"Export_Prospects_" + requestforFileName.FileName + ".xlsx";
                        prospectExportTracker.LastModifiedBy = input.CurrentUserId;
                        prospectExportTracker.CreatedBy = input.CurrentUserId;
                        await _exportProspectTrackerRepo.UpdateAsync(prospectExportTracker, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    prospectExportTracker.Message = ex.Message.ToString();
                    prospectExportTracker.LastModifiedBy = input.CurrentUserId;
                    prospectExportTracker.CreatedBy = input.CurrentUserId;
                    await _exportProspectTrackerRepo.UpdateAsync(prospectExportTracker);
                    if (errorEmailTemplate != null && serviceProvider != null && !isSent)
                    {
                        List<string> toEmails = new();
                        List<string> ccEamils = new();
                        List<string> bccEamils = new();
                        if (prospectExportTracker?.ToRecipients?.Any() ?? false)
                        {
                            toEmails.AddRange(prospectExportTracker.ToRecipients);
                        }
                        if (prospectExportTracker?.CcRecipients?.Any() ?? false)
                        {
                            ccEamils.AddRange(prospectExportTracker.CcRecipients);
                        }
                        if (prospectExportTracker?.BccRecipients?.Any() ?? false)
                        {
                            bccEamils.AddRange(prospectExportTracker.BccRecipients);
                        }
                        emailSenderDto.To = toEmails;
                        emailSenderDto.Cc = ccEamils;
                        emailSenderDto.Bcc = bccEamils;
                        emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                        emailSenderDto.EmailBody = errorEmailTemplate.Body;
                        emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                        emailSenderDto.Subject = errorEmailTemplate?.Subject ?? string.Empty;
                        await _graphEmailService.SendEmail(emailSenderDto);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "FunctionEntryPoint -> ExportProspectsHandler()",
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
        }
        public async Task ExportProspectsByCustomFiltersHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var globalSettings = (await _globalSettingsRepository.ListAsync(cancellationToken)).FirstOrDefault();
            if (globalSettings != null && globalSettings.IsExportDataEnabled)
            {
                var prospectExportTracker = await _exportProspectTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
                if (prospectExportTracker == null)
                {
                    throw new NotFoundException("No Tracker found By This Id");
                }
                var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
                var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
                var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ExportLead), CancellationToken.None)).FirstOrDefault();
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                bool isSent = false;
                try
                {
                    if (prospectExportTracker != null && serviceProvider != null && exportEmailTemplate != null)
                    {
                        #region Fetch All Master Data and Other Data
                        var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                        var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                        var customProspectStatuses = new List<CustomProspectStatus>(await _prospectStatusRepo.ListAsync(cancellationToken));
                        var prospectSources = new List<MasterProspectSource>(await _prospectSourceRepo.ListAsync(cancellationToken));
                        #endregion

                        GetAllProspectRequest? request = JsonConvert.DeserializeObject<GetAllProspectRequest>(prospectExportTracker?.Request ?? string.Empty);
                        RunAWSBatchForExportProspectsByCustomFiltersRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForExportProspectsByCustomFiltersRequest>(prospectExportTracker?.Request ?? string.Empty);
                        List<Guid> subIds = new();
                        request.PageNumber = 1;
                        var isAdmin = await _dapperRepository.IsAdminV2Async(input.CurrentUserId, input.TenantId ?? string.Empty);
                        try
                        {
                            if (request?.AssignTo?.Any() ?? false)
                            {
                                if (request?.IsWithTeam ?? false)
                                {
                                    subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, input.TenantId ?? string.Empty)).ToList();
                                }
                                else
                                {
                                    subIds = request?.AssignTo ?? new List<Guid>();
                                }
                            }
                            else
                            {
                                subIds = (await _dapperRepository.GetSubordinateIdsAsync(input.CurrentUserId, input.TenantId ?? string.Empty, viewAllLeads:null, isAdmin))?.ToList() ?? new();
                            }
                        }
                        catch (Exception e)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                ErrorSource = e?.Source,
                                StackTrace = e?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "FunctionEntryPoint -> ExportProspectsHandler()",
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                            throw;
                        }
                        CustomFilter? filter = null;
                        if (request.CustomFilterId == null || request.CustomFilterId == default)
                        {
                            filter = await _customFilterRepo.FirstOrDefaultAsync(new GetProspectDefaultCustomFilterSpec(input.CurrentUserId, isAdmin), cancellationToken);
                        }
                        else
                        {
                            filter = await _customFilterRepo.FirstOrDefaultAsync(new GetProspectCustomFiltersSpec(request.CustomFilterId ?? Guid.Empty), cancellationToken);
                        }
                        //var prospects = (await _prospectRepository.GetAllProspectsByCustomFiltersForWebAsync(filter ?? new CustomFilter(), request.Adapt<GetAllProspectParameter>(), subIds, input.CurrentUserId, isAdmin, customProspectStatuses)).ToList();
                        List<ExportProspectDto> prospectDto = null;
                        int? notesCount = 0;
                        var prospects = (await _prospectRepository.GetAllProspectsExportByCustomFiltersForWebAsync(filter ?? new CustomFilter(), request.Adapt<GetAllProspectParameter>(), subIds, input.CurrentUserId, isAdmin, customProspectStatuses)).ToList();
                        var prospectsIds = prospects?.Select(i => i.Id).ToList();
                        if (requestforFileName?.IsWithNotes == null || requestforFileName?.IsWithNotes == true)
                        {
                            try
                            {
                                var result = await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<Lrb.Application.DataManagement.Web.Export.Dtos.NotesDetails>("LeadratBlack", "GetAllProspectNoteswithUpdatedDates", new
                                {
                                    p_prospectids = prospectsIds,
                                    p_tenantid = input.TenantId,
                                    p_notescount = requestforFileName?.NotesCount,
                                });
                                var notesDetails = ExportProspectHelper.GetAllDatawithNotes(result.ToList(), requestforFileName?.NotesCount ?? 3);
                                prospectDto = ExportProspectHelper.ConvertToExportProspectDto(prospects, users, customProspectStatuses, prospectSources, propetyTypes, notes: notesDetails.ToList(), isWithNotes: requestforFileName?.IsWithNotes);
                                notesCount = prospectDto.OrderByDescending(i => i.NotesDetails?.Count ?? 0)?.FirstOrDefault()?.NotesDetails?.Count();
                            }
                            catch (Exception ex)
                            {
                                prospectDto = ExportProspectHelper.ConvertToExportProspectDto(prospects, users, customProspectStatuses, prospectSources, propetyTypes);
                            }
                        }
                        else
                        {
                            prospectDto = ExportProspectHelper.ConvertToExportProspectDto(prospects, users, customProspectStatuses, prospectSources, propetyTypes);
                        }
                        var exportTemplate = await _exportTemplateRepo.GetByIdAsync(prospectExportTracker?.TemplateId ?? Guid.Empty);
                        var fileBytes = ExcelHelper.CreateExcelFromList(prospectDto, exportTemplate?.Properties ?? new List<string>(), new List<string> { "Id", "AssignTo" }, requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset, notesCount ?? 3).ToArray();
                        var fileName = ExportProspectHelper.ConvertToString(request.ProspectVisiblity) + "-" + (request.FilterType.ToString() == "AllWithNID" ? "All" : request.FilterType.ToString() == "All" ? "Active" : request.FilterType.ToString());
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Prospects/{input.TenantId ?? "Default"}", /*$"Export-{fileName}-{DateTime.Now.ToString("F")}.xlsx"*/$"Export_Prospects_" + input.TenantId + requestforFileName.FileName + ".xlsx", fileBytes, 0);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        List<string> toEmails = new();
                        List<string> ccEamils = new();
                        List<string> bccEamils = new();
                        if (prospectExportTracker?.ToRecipients?.Any() ?? false)
                        {
                            toEmails.AddRange(prospectExportTracker.ToRecipients);
                        }
                        if (prospectExportTracker?.CcRecipients?.Any() ?? false)
                        {
                            ccEamils.AddRange(prospectExportTracker.CcRecipients);
                        }
                        if (prospectExportTracker?.BccRecipients?.Any() ?? false)
                        {
                            bccEamils.AddRange(prospectExportTracker.BccRecipients);
                        }
                        var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                        emailSenderDto.To = toEmails;
                        emailSenderDto.Cc = ccEamils;
                        emailSenderDto.Bcc = bccEamils;
                        emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                        emailSenderDto.EmailBody = template;
                        emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                        emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                        await _graphEmailService.SendEmail(emailSenderDto);
                        isSent = true;
                        prospectExportTracker.Count = prospects.Count();
                        prospectExportTracker.S3BucketKey = presignedUrl;
                        prospectExportTracker.FileName = $"Export_Prospects_" + requestforFileName.FileName + ".xlsx";
                        prospectExportTracker.Template = JsonConvert.SerializeObject(exportTemplate);
                        prospectExportTracker.LastModifiedBy = input.CurrentUserId;
                        prospectExportTracker.CreatedBy = input.CurrentUserId;
                        await _exportProspectTrackerRepo.UpdateAsync(prospectExportTracker, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    prospectExportTracker.Message = ex.Message.ToString();
                    prospectExportTracker.LastModifiedBy = input.CurrentUserId;
                    prospectExportTracker.CreatedBy = input.CurrentUserId;
                    await _exportProspectTrackerRepo.UpdateAsync(prospectExportTracker);
                    if (errorEmailTemplate != null && serviceProvider != null && !isSent)
                    {
                        List<string> toEmails = new();
                        List<string> ccEamils = new();
                        List<string> bccEamils = new();
                        if (prospectExportTracker?.ToRecipients?.Any() ?? false)
                        {
                            toEmails.AddRange(prospectExportTracker.ToRecipients);
                        }
                        if (prospectExportTracker?.CcRecipients?.Any() ?? false)
                        {
                            ccEamils.AddRange(prospectExportTracker.CcRecipients);
                        }
                        if (prospectExportTracker?.BccRecipients?.Any() ?? false)
                        {
                            bccEamils.AddRange(prospectExportTracker.BccRecipients);
                        }
                        emailSenderDto.To = toEmails;
                        emailSenderDto.Cc = ccEamils;
                        emailSenderDto.Bcc = bccEamils;
                        emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                        emailSenderDto.EmailBody = errorEmailTemplate.Body;
                        emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                        emailSenderDto.Subject = errorEmailTemplate?.Subject ?? string.Empty;
                        await _graphEmailService.SendEmail(emailSenderDto);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "FunctionEntryPoint -> ExportProspectsHandler()",
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
        }
    }
}
