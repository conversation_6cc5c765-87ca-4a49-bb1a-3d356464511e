﻿using Lrb.Application.Reports.Web.Data.Dtos.Common;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace ExcelUpload
{
    public static class ExportDataHelper
    {
        public static List<DataHeadersDto> GetDataHeaders<T>(T data, List<CustomProspectStatus> statuses)
        {
            var headers = new List<DataHeadersDto>();
            var properties = typeof(T).GetProperties();
            foreach (var property in properties)
            {
                if (IsSimpleProperty(property))
                {
                    headers.Add(new DataHeadersDto() { Header = property.Name });
                }
            }
            var baseStats = statuses?.Where(i => i.BaseId == null || i.BaseId == Guid.Empty)?.ToList();
            if (baseStats?.Any() ?? false)
            {
                foreach (var status in baseStats)
                {
                    var value = new DataHeadersDto() { Header = status.DisplayName };
                    var subStats = statuses?.Where(i => i.BaseId == status.Id)?.Select(i => i?.DisplayName ?? string.Empty)?.ToList();
                    if(subStats?.Any() ?? false)
                    {
                        value.SubHeaderNames = new List<string>(subStats);
                    }
                    headers.Add(value);
                }
            }
            return headers;
        }
        public static List<LeadHeadersDto> GetLeadHeaders<T>(List<CustomMasterLeadStatus> statuses, bool ShouldAddOnlyBaseStatus)
        {
            var headers = new List<LeadHeadersDto>();
            var properties = typeof(T).GetProperties();
            foreach (var property in properties)
            {
                if (IsSimpleProperty(property))
                {
                    headers.Add(new LeadHeadersDto() { Header = property.Name });
                }
            }
            var baseStats = statuses?.Where(i => i.BaseId == null || i.BaseId == Guid.Empty)?.OrderBy(i => i.Status)?.ToList();
            var copyHeaders = new List<LeadHeadersDto>();
            if (baseStats?.Any() ?? false)
            {
                foreach (var status in baseStats)
                {
                    var baseValue = new LeadHeadersDto() { Header = status.DisplayName };
                    if (!ShouldAddOnlyBaseStatus)
                    {
                        var subStats = statuses?.Where(i => i.BaseId == status.Id)?.Select(i => i?.DisplayName ?? string.Empty)?.OrderBy(i => i)?.ToList();
                        if (subStats?.Any() ?? false)
                        {
                            var subValue = new LeadHeadersDto() { Header = status.DisplayName };
                            subValue.SubHeaderNames = new List<string>(subStats);
                            copyHeaders.Add(subValue);
                        }
                        else
                        {
                            headers.Add(baseValue);
                        }
                    }
                    else
                    {
                        headers.Add(baseValue);
                    }
                }
                if(copyHeaders?.Any() ?? false)
                {
                    headers.AddRange(copyHeaders);
                }
            }
            return headers;
        }
        private static bool IsSimpleProperty(PropertyInfo property)
        {
            Type propertyType = property.PropertyType;
            return propertyType.IsPrimitive || propertyType == typeof(string) || propertyType == typeof(TimeOnly?);

        }

    }
}
