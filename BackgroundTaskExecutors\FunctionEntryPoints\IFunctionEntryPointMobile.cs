﻿using Lrb.Application.Lead.Mobile.v2;
using System;
using System.Threading.Tasks;

namespace BackgroundTaskExecutors
{
    public interface IFunctionEntryPointMobile
    {
        Task UpdateLeadStatusAsync(V2UpdateLeadStatusRequest input, Guid? currentUserId,string? tenantId = null);
    }
    public interface IFunctionEntryPointMobileV2
    {
        Task SendStatusChangeNotificationAsync(UpdateStatusChangeDto input, Guid? currentUserId, string? tenantId = null);
    }
}
