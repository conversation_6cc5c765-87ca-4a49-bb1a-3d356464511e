﻿using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.UserDetails.Web.Request;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.User;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using VimeoDotNet.Models;
namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task UserDeleteHandler(InputPayload input)

        {
            CancellationToken cancellationToken = CancellationToken.None;
            UserDeletedTracker? userDeleteTracker = await _userDeletedRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            if (userDeleteTracker != null)
            {
                try
                {
                    var request = JsonConvert.DeserializeObject<RunAWSBatchsForUserDeleteRequest>(userDeleteTracker.Request ?? string.Empty);
                    if (request != null)
                    {
                        try
                        {
                            userDeleteTracker.Status = Lrb.Domain.Enums.UploadStatus.Started;
                            userDeleteTracker.CreatedBy = input.CurrentUserId;
                            userDeleteTracker.UserId = request.UserId;
                            await _userDeletedRepo.UpdateAsync(userDeleteTracker);
                            var currentUser = await _userService.GetAsync(input.CurrentUserId.ToString(), cancellationToken);
                            string currentUserName = null;
                            if (currentUser != null)
                            {
                                currentUserName = $"{currentUser.FirstName} {currentUser.LastName}";
                            }

                            var user = (await _userRepository.ListAsync(new GetUserDetailsByIdSpec(request.UserId), cancellationToken)).FirstOrDefault();
                            if (user == null)
                            {
                                throw new Exception("No user found.");
                            }
                            var tenantId = _currentUser.GetTenant();
                            var isAdmin = _dapperRepository.IsAdminAsync(request.UserId, tenantId ?? string.Empty).Result;
                            if (isAdmin == true)
                            {
                                throw new Exception("Administrators Profile's cannot be delete");
                            }
                            var leads = await _leadRepo.ListAsync(new GetAssignedLeadsByUserIdSpec(user.UserId), cancellationToken);
                            var leadsarCount = leads.Count(i => i.IsArchived == true);
                            var Totalcount = leads.Count - leadsarCount;
                            userDeleteTracker.LeadsCount = Totalcount;
                            var secLeads = await _leadRepo.ListAsync(new GetSecAssignedLeadsByUserIdSpec(user.UserId), cancellationToken);
                            var secArLeadCount = secLeads.Count(i => i.IsArchived == true);
                            var TotalSecCount = secLeads.Count - secArLeadCount;
                            userDeleteTracker.SecondaryLeadsCount = TotalSecCount;
                            var CloseManger = await _leadRepo.ListAsync(new GetCloseMangerSourceMangerAssignedLeadsByUserIdSpec(user.UserId), cancellationToken);
                            var ClosedataManger = await _prospectRepo.ListAsync(new GetCloseMangerAssignedProspectByUserIdSpec(user.UserId), cancellationToken);
                            var prospects = await _prospectRepo.ListAsync(new GetAssignedProspectByUserIdSpec(user.UserId), cancellationToken);
                            var prospectArCount = prospects.Count(i => i.IsArchived == true);
                            var TotalProspects = prospects.Count - prospectArCount;
                            userDeleteTracker.ProspectsCount = TotalProspects;
                            var projects = await _projectRepo.ListAsync(new GetAssignedProjectByUserIdSpec(user.UserId), cancellationToken);
                            var properties = await _propertyAssignment.ListAsync(new GetAssignedPropertyByUserIdAssignToSpec(user.UserId), cancellationToken);
                            var assignments = await _assignmentRepo.ListAsync(new UserAssignmentspace(user.UserId), cancellationToken);
                            var secAssignments = await _assignmentRepo.ListAsync(new UserSecAssignmentspace(user.UserId), cancellationToken);
                            var dupAssignments = await _assignmentRepo.ListAsync(new UserDupAssignmentspace(user.UserId), cancellationToken);
                            userDeleteTracker.Status = Lrb.Domain.Enums.UploadStatus.InProgress;
                            await _userDeletedRepo.UpdateAsync(userDeleteTracker);
                            var attendencesettings = await _attendenceRepo.ListAsync(new UserAttendencespace(user.UserId), cancellationToken);
                            var customs = await _customemailRepo.ListAsync(new UserCustomEmailInfopace(user.UserId), cancellationToken);
                            var leadsAssignRotationInfo = await _leadrotationRepo.ListAsync(new UserLeadsAssignRotationInfopace(user.UserId), cancellationToken);
                            var leadsGroupMangerRotationInfo = await _leadrotationRepo.ListAsync(new UserLeadsGroupManagerRotationInfopace(user.UserId), cancellationToken);
                            var reportsTo = await _userDetailsRepo.ListAsync(new ReportsToUserIdsSpec(user.UserId), cancellationToken);
                            var propertiesV1 = await _propertyRepo.ListAsync(new GetAssignedPropertyByListingOnBehalfUserIdSpec(user.UserId), cancellationToken);

                            if (leads.Count > 0)
                            {
                                try
                                {
                                    await _dapperRepository.UpdateLead(user.UserId);
                                    await _dapperRepository.UpdateLeadHistory(leads.Select(i => i.Id).ToList());
                                }
                                catch
                                {
                                }
                            }
                            if (secLeads.Count > 0)
                            {
                                Parallel.ForEach(secLeads, i =>
                                {
                                    i.SecondaryUserId = Guid.Empty;
                                });
                                await _leadRepo.UpdateRangeAsync(secLeads);
                                try
                                {
                                    await _dapperRepository.UpdateSecLeadHistory(secLeads.Select(i => i.Id).ToList(), input.CurrentUserId, currentUserName);

                                }
                                catch
                                {
                                }
                            }
                            if (CloseManger.Count > 0)
                            {
                                foreach (var lead in CloseManger)
                                {
                                    if (lead.SourcingManager == request.UserId)
                                    {
                                        lead.SourcingManager = Guid.Empty;
                                    }
                                    if (lead.ClosingManager == request.UserId)
                                    {
                                        lead.ClosingManager = Guid.Empty;
                                    }

                                }
                                await _leadRepo.UpdateRangeAsync(CloseManger);
                                foreach (var lead in CloseManger)
                                {
                                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                                    var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(lead.Id), cancellationToken))?.FirstOrDefault();
                                    var leadDto = fullLead?.Adapt<ViewLeadDto>();
                                    await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                                    await UpdateLeadHistoryAsync(lead, leadDto, cancellationToken: cancellationToken);
                                }
                            }
                            if (prospects.Count > 0)
                            {
                                Parallel.ForEach(prospects, prospect =>
                                {
                                    prospect.AssignTo = Guid.Empty;

                                });
                                await _prospectRepo.UpdateRangeAsync(prospects);
                            }
                            if (ClosedataManger.Count > 0)
                            {
                                foreach (var prospect in ClosedataManger)
                                {
                                    if (prospect.ClosingManager == request.UserId)
                                    {
                                        prospect.ClosingManager = Guid.Empty;
                                    }
                                    if (prospect.SourcingManager == request.UserId)
                                    {
                                        prospect.SourcingManager = Guid.Empty;
                                    }
                                    await _prospectRepo.UpdateAsync(prospect);

                                }
                            }
                            if (assignments.Count > 0)
                            {
                                foreach (var assignment in assignments)
                                {
                                    if (assignment.LastAssignedUser == request.UserId)
                                    {
                                        assignment.LastAssignedUser = Guid.Empty;

                                    }
                                    if (assignment.PreviousAssignedUser == request.UserId)
                                    {
                                        assignment.PreviousAssignedUser = Guid.Empty;

                                    }
                                    if (assignment.NextUserToBeAssigned == request.UserId)
                                    {
                                        assignment.NextUserToBeAssigned = Guid.Empty;
                                    }
                                    if (assignment.UserIds != null && assignment.UserIds.Contains(request.UserId))
                                    {
                                        assignment.UserIds.RemoveAll(id => id == request.UserId);
                                    }
                                }
                                await _assignmentRepo.UpdateRangeAsync(assignments);
                            }

                            if (secAssignments.Count > 0)
                            {
                                foreach (var secAssignment in secAssignments)
                                {
                                    if (secAssignment.SecondaryUserIds != null)
                                    {
                                        secAssignment.SecondaryUserIds.RemoveAll(id => id == request.UserId);
                                    }

                                }
                                await _assignmentRepo.UpdateRangeAsync(secAssignments);
                            }
                            if (dupAssignments.Count > 0)
                            {
                                foreach (var secAssignment in dupAssignments)
                                {
                                    if (secAssignment.DuplicateUserIds != null)
                                    {
                                        secAssignment.DuplicateUserIds.RemoveAll(id => id == request.UserId);
                                    }
                                }
                                await _assignmentRepo.UpdateRangeAsync(dupAssignments);

                            }
                            if (attendencesettings.Count > 0)
                            {
                                foreach (var assignment in attendencesettings)
                                {
                                    if (assignment.UserIds != null)
                                    {
                                        assignment.UserIds.RemoveAll(id => id == request.UserId);
                                    }
                                }
                                await _attendenceRepo.UpdateRangeAsync(attendencesettings);
                            }

                            if (properties.Count > 0)
                            {
                                try
                                {
                                    await _dapperRepository.UpdatePropertyAssignentAsync(user.UserId);
                                }
                                catch
                                {

                                }

                            }

                            if (propertiesV1.Count > 0)
                            {
                                try
                                {
                                    await _dapperRepository.UpdatePropertyListingOnBehalfAsync(user.UserId);
                                }
                                catch
                                {

                                }

                            }
                            if (customs.Count > 0)
                            {
                                foreach (var assignment in customs)
                                {
                                    if (assignment.UserIds != null)
                                    {
                                        assignment.UserIds.RemoveAll(id => id == request.UserId);
                                    }
                                }
                                await _customemailRepo.UpdateRangeAsync(customs);
                            }
                            if (leadsAssignRotationInfo.Count > 0)
                            {
                                foreach (var assignment in leadsAssignRotationInfo)
                                {
                                    if (assignment.AssignmentGroup != null)
                                    {
                                        assignment.AssignmentGroup.RemoveAll(id => id == request.UserId);
                                    }
                                }
                                await _leadrotationRepo.UpdateRangeAsync(leadsAssignRotationInfo);
                            }
                            if (leadsGroupMangerRotationInfo.Count > 0)
                            {
                                foreach (var lead in leadsGroupMangerRotationInfo)
                                {
                                    lead.GroupManager = Guid.Empty;
                                }
                                await _leadrotationRepo.UpdateRangeAsync(leadsGroupMangerRotationInfo);
                            }
                            if (reportsTo.Count() > 0)
                            {
                                Parallel.ForEach(reportsTo, i =>
                                {
                                    i.ReportsTo = Guid.Empty;

                                });
                                await _userDetailsRepo.UpdateRangeAsync(reportsTo);
                            }
                            await _userRepository.SoftDeleteAsync(user, cancellationToken);
                            var deleteduser = await _userService.PermanentDeleteAsync(request.UserId.ToString(), cancellationToken);
                            var deleteduserDetails = deleteduser.Adapt<DeletedUser>();
                            await _deleteUsers.AddAsync(deleteduserDetails);
                            userDeleteTracker.Status = Lrb.Domain.Enums.UploadStatus.Completed;
                            await _userDeletedRepo.UpdateAsync(userDeleteTracker);
                        }
                        catch
                        {

                        }
                    }

                }
                catch (Exception ex)
                {
                    userDeleteTracker.Status = Lrb.Domain.Enums.UploadStatus.Failed;
                    userDeleteTracker.Message = ex.Message;
                    await _userDeletedRepo.UpdateAsync(userDeleteTracker);
                }
            }
        }

        protected async Task UpdateLeadHistoryAsync(Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null, bool isLeadUpdateRequest = false)
        {
            try
            {
                var userId = currentUserId ?? _currentUser.GetUserId();
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = userId;
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                    }
                    else
                    {
                        await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        protected async Task<ViewLeadDto> GetFullLeadDtoAsync(Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new Exception("Lead not found by the provided id.");
                var leadDto = fullLead.Adapt<ViewLeadDto>();
                if (leadDto.Address != null)
                {
                    leadDto.Address.Id = Guid.NewGuid();
                }
                if (leadDto.ChannelPartners?.Any() ?? false)
                {
                    leadDto.ChannelPartners.ForEach(cp => cp.Id = Guid.NewGuid());
                }
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                return leadDto;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

    }
}