﻿using Amazon.CognitoIdentityProvider.Model.Internal.MarshallTransformations;
using Dapper;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Agency;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application.ChannelPartner;
using Lrb.Application.DataManagement.Web;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Request.Bulk_Upload;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Project;
using Lrb.Application.Property;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.DataManagement;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Shared.Utils;
using Mapster;
using Newtonsoft.Json;
using Npgsql;
using System.Collections.Concurrent;
using System.Data;
using static Lrb.Application.DataManagement.Web.Specs.GetProspectByContactNoSpecs;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint : IFunctionEntryPoint
    {
        public async Task<DataTable> GetDataTableAsync(DataMigrateTracker dataMigrateTracker)
        {
            #region Convert To DataTable
            Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", dataMigrateTracker.S3BucketKey);
            DataTable dataTable = new();
            if (dataMigrateTracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
            {
                using MemoryStream memoryStream = new();
                fileStream.CopyTo(memoryStream);
                dataTable = CSVHelper.CSVToDataTable(memoryStream);
            }
            else
            {
                dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, dataMigrateTracker.SheetName);
            }

            List<InvalidProspect> invalids = new();
            int totalRows = dataTable.Rows.Count;
            for (int i = totalRows - 1; i >= 0; i--)
            {
                var row = dataTable.Rows[i];
                var data1 = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.Name]].ToString();
                var data2 = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.ContactNo]].ToString();
                if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                {
                    row.Delete();
                }
                else if (string.IsNullOrEmpty(row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.Name]].ToString()) && string.IsNullOrEmpty(row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.ContactNo]].ToString()))
                {
                    var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i.ToString())));
                    var invalidData = new InvalidProspect
                    {
                        Errors = "contact number and name are empty",
                        Notes = notes
                    };
                    if (!invalids.Any(i => i.Notes == invalidData.Notes))
                    {
                        invalids.Add(invalidData);
                    }
                    row.Delete();
                }
            }
            if (dataTable.Rows.Count <= 0)
            {
                throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
            }
            totalRows = dataTable.Rows.Count;
            Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
            #endregion

            return dataTable;
        }
        public async Task<MasterItems> GetMasterItemsAsync(DataTable dataTable, DataMigrateTracker dataMigrateTracker, string currentUserId, string tenantId)
        {
            #region featching data from excel
            List<string> properties = new List<string>();
            List<string> projects = new List<string>();
            List<string> agencies = new List<string>();
            List<string> channelPartners = new List<string>();
            List<string>? assignedToUsers = new();
            List<string> campaigns = new List<string>();
            if (((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.Property) ?? false))
                || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.Project) ?? false))
                || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.AgencyName) ?? false))
                || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.ChannelPartnerName) ?? false))
                || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.AssignToUser) ?? false))
                || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.CampaignName) ?? false))
                || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.SourcingManager) ?? false))
                || ((dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.ClosingManager) ?? false)))
            {
                var isPropertyPresent = (dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.Property) ?? false);
                var isProjectPresent = (dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.Project) ?? false);
                var isAgencyNamePresent = (dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.AgencyName) ?? false);
                var isChannelPresent = (dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.ChannelPartnerName) ?? false);
                var isassignedToUserPresent = (dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.AssignToUser) ?? false);
                var isCampaignPresent = (dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.CampaignName) ?? false);
                var isSourcingManagerPresent = (dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.SourcingManager) ?? false);
                var isClosingManagerPresent = (dataMigrateTracker.MappedColumnsData?.ContainsKey(DataMigrateColumns.ClosingManager) ?? false);

                dataTable.AsEnumerable().ToList().ForEach(row =>
                {
                    if (isPropertyPresent)
                    {
                        var propertyName = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.Property]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(propertyName))
                        {
                            properties.Add(propertyName.Trim());
                        }
                    }
                    if (isProjectPresent)
                    {
                        var projectName = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.Project]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(projectName))
                        {
                            projects.Add(projectName.Trim());
                        }
                    }
                    if (isAgencyNamePresent)
                    {
                        var agencyName = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.AgencyName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(agencyName))
                        {
                            agencies.Add(agencyName.Trim());
                        }
                    }
                    if (isChannelPresent)
                    {
                        var cpName = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.ChannelPartnerName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(cpName))
                        {
                            channelPartners.Add(cpName.Trim());
                        }
                    }
                    if (isassignedToUserPresent)
                    {
                        var assinedUser = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.AssignToUser]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(assinedUser))
                        {
                            assignedToUsers.Add(assinedUser.Trim().ToLower());
                        }
                    }
                    if (isCampaignPresent)
                    {
                        var campaignName = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.CampaignName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(campaignName))
                        {
                            campaigns.Add(campaignName.Trim());
                        }
                    }
                    if (isSourcingManagerPresent)
                    {
                        var sourcingUser = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.SourcingManager]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(sourcingUser))
                        {
                            assignedToUsers.Add(sourcingUser.Trim().ToLower());
                        }
                    }
                    if (isClosingManagerPresent)
                    {
                        var closingUser = row[dataMigrateTracker.MappedColumnsData[DataMigrateColumns.ClosingManager]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(closingUser))
                        {
                            assignedToUsers.Add(closingUser.Trim().ToLower());
                        }
                    }
                });
            }
            #endregion

            var masterItems = new Lrb.Application.DataManagement.Web.Dtos.MasterItems();
            var cancellationToken = CancellationToken.None;
            var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");
            masterItems.TenantDisplayPrefix = await _dapperRepository.GetDisplayIndexPrefixByTenantIdAsync(tenantId);
            #region Projects
            var allProjects = await _newProjectRepo.ListAsync(new GetAllProjectByNamesSpec(projects.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            allProjects = allProjects.GroupBy(p => p.Name.ToLower().Trim()).Select(g => g.First()).ToList();
            var remainingProjects = projects.Select(i => i).ToHashSet()
                .Where(project => !allProjects.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(project.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingProjects?.Any() ?? false)
            {
                var maxserial = await _dapperRepository.GetProjectMaxSerialNumberAsync(masterItems.TenantDisplayPrefix ?? string.Empty);
                var newprojects = remainingProjects
                    .Select(i =>
                    {
                        var serialNumber = BulkUploadHelper.GenerateDisplayIndexPrefixAsync(masterItems.TenantDisplayPrefix ?? string.Empty, maxserial);
                        maxserial = serialNumber.Item2;
                        return new Project
                        {
                            Id = Guid.NewGuid(),
                            Name = i,
                            CreatedOn = DateTime.UtcNow,
                            LastModifiedOn = DateTime.UtcNow,
                            SerialNo = serialNumber.Item1,
                        };
                    }).ToList();
                var newProjects = await CreateProejctsInDapperAsync(newprojects, connection, tenantId);
                if (newProjects.Item1 != null)
                {
                    allProjects.AddRange(newProjects.Item1);
                }
            }
            var deletedProjects = allProjects.Where(i => i.IsArchived).ToList();
            allProjects.RemoveAll(p => deletedProjects.Any(d => d.Id == p.Id));
            if (deletedProjects?.Any() ?? false)
            {
                var restoredProjects = await RestoreProejctsInDapperAsync(deletedProjects, connection, tenantId);
                if (restoredProjects.Item1 != null)
                {
                    allProjects.AddRange(restoredProjects.Item1);
                }
            }
            masterItems.Projects = allProjects;
            #endregion

            #region Properties
            var allProperties = await _propertyRepo.ListAsync(new GetAllPropertyByTitlesSpec(properties.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            allProperties = allProperties.GroupBy(p => p.Title.ToLower().Trim()).Select(g => g.First()).ToList();
            var remainingProperties = properties.Select(i => i).ToHashSet()
                .Where(property => !string.IsNullOrEmpty(property) && !allProperties.ConvertAll(i => i.Title.ToLower().Trim().Replace(" ", "")).Contains(property.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingProperties?.Any() ?? false)
            {
                var maxserial = await _dapperRepository.GetPropertiesMaxSerialNumberAsync(masterItems.TenantDisplayPrefix ?? string.Empty);
                var newproperties = remainingProperties
                    .Select(i =>
                    {
                        var serialNumber = BulkUploadHelper.GenerateDisplayIndexPrefixAsync(masterItems.TenantDisplayPrefix ?? string.Empty, maxserial);
                        maxserial = serialNumber.Item2;
                        return new Property
                        {
                            Id = Guid.NewGuid(),
                            Title = i,
                            CreatedOn = DateTime.UtcNow,
                            LastModifiedOn = DateTime.UtcNow,
                            SerialNo = serialNumber.Item1,
                        };
                    }).ToList();
                var newProperties = await CreateProrpertyInDapperAsync(newproperties, connection, tenantId);
                if (newProperties.Item1 != null)
                {
                    allProperties.AddRange(newProperties.Item1);
                }
            }
            var deletedProperties = allProperties.Where(i => i.IsArchived).ToList();
            allProperties.RemoveAll(p => deletedProperties.Any(d => d.Id == p.Id));
            if (deletedProperties?.Any() ?? false)
            {
                var restoredProperties = await RestorePropertiesInDapperAsync(deletedProperties, connection, tenantId);
                if (restoredProperties.Item1 != null)
                {
                    allProperties.AddRange(restoredProperties.Item1);
                }
            }
            masterItems.Properties = allProperties;
            #endregion

            #region Agencies
            var allAgencies = await _agencyRepo.ListAsync(new GetAllAgencyByNameSpec(agencies.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingAgencies = agencies.Select(i => i).ToHashSet()
                .Where(agency => !string.IsNullOrEmpty(agency) && !allAgencies.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(agency.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingAgencies?.Any() ?? false)
            {
                var newagencies = remainingAgencies.Select(i => new Agency { Id = Guid.NewGuid(), Name = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var newAgencies = await CreateAgencyInDapperAsync(newagencies, connection, tenantId);
                if (newAgencies.Item1 != null)
                {
                    allAgencies.AddRange(newAgencies.Item1);
                }
            }
            masterItems.Agencies = allAgencies;
            #endregion

            #region ChannelPartner
            var allChannelPartner = await _cpRepository.ListAsync(new GetAllChannelPartnerByNameSpec(channelPartners.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingChannelPartner = channelPartners.Select(i => i).ToHashSet()
                .Where(channelPartner => !string.IsNullOrEmpty(channelPartner) && !allChannelPartner.ConvertAll(i => i.FirmName.ToLower().Trim().Replace(" ", "")).Contains(channelPartner.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingChannelPartner?.Any() ?? false)
            {
                var newchannelPartner = remainingChannelPartner.Select(i => new ChannelPartner { Id = Guid.NewGuid(), FirmName = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var newChannelPartner = await CreateChannalPartnerInDapperAsync(newchannelPartner, connection, tenantId);
                if (newChannelPartner.Item1 != null)
                {
                    allChannelPartner.AddRange(newChannelPartner.Item1);
                }
            }
            masterItems.ChannelPartners = allChannelPartner;
            #endregion

            #region CommonData
            masterItems.PropetyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
            masterItems.ProspectStatuses = await _prospectStatusRepo.ListAsync(CancellationToken.None);
            masterItems.ProspectSources = await _prospectSourceRepo.ListAsync(CancellationToken.None);
            masterItems.GlobalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);
            masterItems.AreaUnits = await _masterAreaUnitRepo.ListAsync(cancellationToken);
            #endregion

            #region Users
            var userIds = new List<string> { dataMigrateTracker.CreatedBy.ToString(),
                    dataMigrateTracker.LastModifiedBy.ToString(), currentUserId }.ToList().ConvertAll(i => Guid.Parse(i));
            masterItems.HistoryUsers = await _userViewRepo.ListAsync(new UserViewByIdSpec(userIds));
            var users = await _userViewRepo.ListAsync(new GetUsersByUsernamesSpec(assignedToUsers?.Distinct().ToList() ?? new()), cancellationToken);
            if (users?.Any() ?? false)
            {
                if (masterItems.Users == null)
                {
                    masterItems.Users = new List<UserView>();
                }
                masterItems.Users.AddRange(users);
                masterItems.HistoryUsers.AddRange(users);
            }
            #endregion
            #region Campaign
            var allCampaigns = await _campaignRepo.ListAsync(new GetCampaignByNameSpec(campaigns.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingCampaigns = campaigns.Select(i => i).ToHashSet()
                .Where(campaign => !string.IsNullOrEmpty(campaign) && !allCampaigns.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(campaign.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingCampaigns?.Any() ?? false)
            {
                var newCampaign = remainingCampaigns.Select(i => new Campaign { Id = Guid.NewGuid(), Name = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var updatedCampaign = await CreateCampaignInDapperAsync(newCampaign, connection, tenantId);
                if (updatedCampaign.Item1 != null)
                {
                    allCampaigns.AddRange(updatedCampaign.Item1);
                }
            }
            masterItems.Campaigns = allCampaigns;
            #endregion
            return masterItems;
        }
        public async Task MigrateDataHandlerV2(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            DataMigrateTracker? dataMigrateTracker = await _dataMigrateTrackerRepo.GetByIdAsync(input.TrackerId);
            Console.WriteLine($"handler() -> DataMigrateTracker GetById(): {JsonConvert.SerializeObject(dataMigrateTracker)}");
            try
            {
                if (dataMigrateTracker != null)
                {
                    try
                    {
                        // updating tracker
                        dataMigrateTracker.MappedColumnsData = dataMigrateTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        dataMigrateTracker.Status = UploadStatus.Started;
                        dataMigrateTracker.LastModifiedBy = input.CurrentUserId;
                        dataMigrateTracker.CreatedBy = input.CurrentUserId;
                        dataMigrateTracker.SheetName = dataMigrateTracker.S3BucketKey.Split('/').Last() + "/" + dataMigrateTracker.SheetName;
                        var migrationType = dataMigrateTracker.MigrationType;
                        await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                        Console.WriteLine($"handler() -> DataMigrateTracker Updated Status: {dataMigrateTracker.Status} \n {JsonConvert.SerializeObject(dataMigrateTracker)}");

                        // retrieving the data from excel  
                        DataTable dataTable = await GetDataTableAsync(dataMigrateTracker);

                        //retrieving the master items  
                        var masterItems = await GetMasterItemsAsync(dataTable, dataMigrateTracker, input.CurrentUserId.ToString(), input.TenantId);

                        // Un Mapped Columns
                        var unMappedColumns = dataTable.GetUnmappedColumnNames(dataMigrateTracker.MappedColumnsData ?? new());
                        var results = dataTable.DataMigrateAsyncV2(dataMigrateTracker.MappedColumnsData ?? new(), unMappedColumns, masterItems, dataMigrateTracker, _sourceRepo, input.JsonData ?? string.Empty);
                        List<Prospect> prospects = results.Item1;
                        var invalids = results.Item2;

                        if (migrationType == DataMigrationType.UpdateMissingInformation || migrationType == DataMigrationType.OverideExisitingProspectInformation)
                        {
                            //update Tracker
                            prospects = prospects.DistinctBy(i => i.ContactNo).ToList();
                            dataMigrateTracker.Status = UploadStatus.InProgress;
                            dataMigrateTracker.TotalCount = dataTable.Rows.Count;
                            dataMigrateTracker.DistinctProspectCount = prospects.Count();
                            dataMigrateTracker.LastModifiedBy = input.CurrentUserId;
                            dataMigrateTracker.DuplicateCount = 0;
                            dataMigrateTracker.CreatedBy = input.CurrentUserId;
                            dataMigrateTracker.TotalUploadedCount = 0;

                            if (invalids.Any())
                            {
                                dataMigrateTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty" || i.Errors == "Duplicate ContactNo" || i.Errors == "Invalid Source").Count();
                                byte[] bytes = ProspectHelper.CreateExcelData(invalids).ToArray();
                                string fileName = $"InvalidProspect-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                                string folder = $"{input.TenantId}/Prospects/Migrate";
                                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                                var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                                dataMigrateTracker.InvalidProspectS3BucketKey = key;
                            }
                            await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);

                            BulkMigrateBackgroundDto backgroundDto = new();
                            if (prospects?.Any() ?? false)
                            {
                                int leadsPerchunk = prospects.Count > 5000 ? 5000 : prospects.Count;
                                var chunks = prospects.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Prospect>(i));

                                List<Task> tasks = new();
                                var currentUserId = _currentUser.GetUserId();
                                var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                                if (currentUserId == Guid.Empty)
                                {
                                    currentUserId = input.CurrentUserId;
                                }
                                Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                                var chunkIndex = 1;

                                foreach (var chunk in chunks.ToList())
                                {
                                    backgroundDto = new Lrb.Application.DataManagement.Web.Dtos.BulkMigrateBackgroundDto()
                                    {
                                        CurrentUserId = currentUserId,
                                        TrackerId = dataMigrateTracker.Id,
                                        TenantInfoDto = tenantInfo,
                                        CancellationToken = CancellationToken.None,
                                        Prospects = new(chunk),
                                        UserViews = masterItems.Users,
                                        HistoryUsers = masterItems.HistoryUsers,
                                        MigrationType = migrationType
                                    };
                                    if (backgroundDto.Prospects.Any())
                                    {
                                        await V2UpdateDuplicateDataAsync(backgroundDto, masterItems, dataMigrateTracker);
                                        chunkIndex++;
                                    }
                                }
                                dataMigrateTracker.ProspectUpdatedCount = prospects.Count();
                                dataMigrateTracker.LastModifiedOn = DateTime.UtcNow;
                                dataMigrateTracker.Status = UploadStatus.Completed;
                                await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                            }
                            else
                            {
                                dataMigrateTracker.TotalUploadedCount = 0;
                                dataMigrateTracker.LastModifiedOn = DateTime.UtcNow;
                                dataMigrateTracker.Status = UploadStatus.Completed;
                                await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                            }
                        }
                        else
                        {
                            List<Prospect> duplicateProspects = new();
                            if (migrationType != DataMigrationType.CreateDuplicateData)
                            {
                                prospects = prospects.DistinctBy(i => i.ContactNo).ToList();
                                List<Lrb.Application.Lead.Web.DuplicateDto> existingProspects = (await _dapperRepository.CheckDuplicatePropsectsAsync(prospects.Select(i => i.ContactNo).ToList() ?? new(), prospects.Where(i => !string.IsNullOrEmpty(i.AlternateContactNo))?.Select(i => i.AlternateContactNo ?? string.Empty)?.ToList() ?? new(), input.TenantId)).ToList();
                                var existingContactNos = existingProspects.Where(i => !string.IsNullOrEmpty(i.ContactNo)).Select(i => i.ContactNo).ToList().Concat(existingProspects.Where(i => !string.IsNullOrEmpty(i.AlternateContactNo)).Select(i => i.AlternateContactNo).ToList());
                                duplicateProspects = prospects.Where(i => existingContactNos.Contains(i.ContactNo) || existingContactNos.Contains(i.AlternateContactNo)).ToList();

                                prospects.RemoveAll(i => existingContactNos.Contains(i.ContactNo) || existingContactNos.Contains(i.AlternateContactNo));
                                if (duplicateProspects?.Any() ?? false)
                                {
                                    var duplicateProspect = duplicateProspects
                                   .Select(prospect =>
                                   {
                                       var user = masterItems.Users?.FirstOrDefault(i => i.Id == prospect.AssignTo);
                                       var assignToUser = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == user?.UserName.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                                       var invalidProspect = prospect.Adapt<InvalidProspect>();
                                       invalidProspect.Errors = "Duplicate Prospect";
                                       invalidProspect.AssignTo = assignToUser != null ? assignToUser.FirstName + " " + assignToUser.LastName : string.Empty;
                                       invalidProspect.Source = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Source?.DisplayName?.ToString() ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                                       invalidProspect.SubSource = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                                       invalidProspect.Status = prospect.Status?.DisplayName ?? string.Empty;
                                       invalidProspect.Created = prospect.CreatedOn.Date;
                                       return invalidProspect;
                                   })
                                   .ToList();
                                    invalids.AddRange(duplicateProspect);
                                }
                            }
                            //update Tracker
                            dataMigrateTracker.Status = UploadStatus.InProgress;
                            dataMigrateTracker.TotalCount = dataTable.Rows.Count;
                            dataMigrateTracker.DistinctProspectCount = prospects.Count() + results.Item2.Count();
                            dataMigrateTracker.LastModifiedBy = input.CurrentUserId;
                            dataMigrateTracker.CreatedBy = input.CurrentUserId;

                            if (invalids.Any())
                            {
                                dataMigrateTracker.DuplicateCount = duplicateProspects?.Count() ?? 0;
                                dataMigrateTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty" || i.Errors == "Invalid Source").Count();
                                byte[] bytes = ProspectHelper.CreateExcelData(invalids).ToArray();
                                string fileName = $"InvalidProspect-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                                string folder = $"{input.TenantId}/Prospects/Migrate";
                                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                                var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                                dataMigrateTracker.InvalidProspectS3BucketKey = key;
                            }
                            await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                            Console.WriteLine($"handler() -> DataMigrateTracker Updated Status: {dataMigrateTracker.Status} \n {JsonConvert.SerializeObject(dataMigrateTracker)}");
                            BulkMigrateBackgroundDto backgroundDto = new();
                            if (prospects?.Any() ?? false)
                            {
                                int prospectsPerchunk = prospects.Count > 5000 ? 5000 : prospects.Count;
                                var chunks = prospects.Chunk(prospectsPerchunk).Select(i => new ConcurrentBag<Prospect>(i));
                                List<Task> tasks = new();
                                var currentUserId = _currentUser.GetUserId();
                                var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                                if (currentUserId == Guid.Empty)
                                {
                                    currentUserId = input.CurrentUserId;
                                }
                                Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                                var chunkIndex = 1;
                                foreach (var chunk in chunks.ToList())
                                {
                                    backgroundDto = new BulkMigrateBackgroundDto()
                                    {
                                        CurrentUserId = currentUserId,
                                        TrackerId = dataMigrateTracker.Id,
                                        TenantInfoDto = tenantInfo,
                                        CancellationToken = CancellationToken.None,
                                        Prospects = new(chunk),
                                        UserViews = masterItems.Users,
                                        HistoryUsers = masterItems.HistoryUsers,
                                        MigrationType = migrationType
                                    };
                                    await V2ExecuteDBOperationsDataAsync(backgroundDto, masterItems, dataMigrateTracker);
                                    chunkIndex++;
                                }
                                dataMigrateTracker.TotalUploadedCount = prospects.Count();
                                dataMigrateTracker.LastModifiedOn = DateTime.UtcNow;
                                dataMigrateTracker.Status = UploadStatus.Completed;
                                await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                            }
                            else
                            {
                                dataMigrateTracker.TotalUploadedCount = 0;
                                dataMigrateTracker.LastModifiedOn = DateTime.UtcNow;
                                dataMigrateTracker.Status = UploadStatus.Completed;
                                await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        dataMigrateTracker = await _dataMigrateTrackerRepo.GetByIdAsync(dataMigrateTracker.Id);
                        dataMigrateTracker.Status = UploadStatus.Failed;
                        dataMigrateTracker.Message = ex.Message;
                        dataMigrateTracker.LastModifiedBy = input.CurrentUserId;
                        dataMigrateTracker.CreatedBy = input.CurrentUserId;
                        await _dataMigrateTrackerRepo.UpdateAsync(dataMigrateTracker);
                    }
                }
                else
                {
                    Console.WriteLine($"handler() -> tracker not found by the Id : {input.TrackerId}");
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "ExecuteDBOperationsAsync -> MigrateDataHandlerV2()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
        }
        private async Task V2ExecuteDBOperationsDataAsync(BulkMigrateBackgroundDto dto, MasterItems masterItems, DataMigrateTracker? tracker)
        {
            if (!(dto.Prospects?.Any() ?? false))
            {
                return;
            }
            try
            {
                var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");
                var prospectDetails = await CreateProspectInDapperAsync(dto.Prospects, connection, dto.TenantInfoDto?.Id ?? string.Empty, dto.CurrentUserId);
                var addresses = ExtractAddressFromProspects(prospectDetails);
                if (addresses?.Any() ?? false)
                {
                    await V3AddBulkLocations(addresses, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                }
                List<ProspectHistory> prospectHistories = new();
                var prospectVM = prospectDetails.Adapt<List<Lrb.Application.DataManagement.Web.Dtos.ViewProspectDto>>();
                Lrb.Application.Identity.Users.UserDetailsDto? user = masterItems.HistoryUsers?.Adapt<List<Lrb.Application.Identity.Users.UserDetailsDto>>().FirstOrDefault(i => i.Id == dto.CurrentUserId);
                List<ProspectHistory> allHistories = new();
                prospectVM.ForEach(prospectDto =>
                {
                    prospectDto.V2SetUserViewForProspect(dto.HistoryUsers?.Adapt<List<Lrb.Application.Identity.Users.UserDetailsDto>>() ?? new(), currentUserId: dto.CurrentUserId);
                    var histories = ProspectHistoryHelper.V2CreateProspectHistoryForVM(prospectDto, null, user, 1, masterItems.ProspectStatuses ?? new(), masterItems.PropetyTypes ?? new(), masterItems.ProspectSources ?? new(), _userService, dto.CancellationToken);
                    allHistories.AddRange(histories);
                });

                await CreateProspectHistoryInDapperAsync(allHistories, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Prospects.Count;
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _dataMigrateTrackerRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsDataAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker != null)
                {
                    if (tracker.TotalUploadedCount == (tracker.DistinctProspectCount - tracker.InvalidCount - tracker.DuplicateCount))
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    else
                    {
                        tracker.Status = UploadStatus.Failed;
                        tracker.Message = e?.InnerException?.Message ?? e?.Message;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _dataMigrateTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkDatadataUploadTrackerUsingEPPlus -> ExecuteDBOperationsDataAsync()",
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        private async Task V2UpdateDuplicateDataAsync(Lrb.Application.DataManagement.Web.Dtos.BulkMigrateBackgroundDto dto, MasterItems masterItems, DataMigrateTracker? tracker)
        {
            List<Prospect> prospectsToUpdate = new();
            List<ViewProspectDto> oldProspects = new();

            try
            {
                if (dto.Prospects?.Any() ?? false)
                {
                    var contactNos = dto.Prospects.Select(i => i.ContactNo).Concat(dto.Prospects.Select(i => i.AlternateContactNo)).Distinct().ToList();
                    if (tracker.MigrationType == DataMigrationType.UpdateMissingInformation)
                    {
                        var duplicateDatasToUpdate = await _prospectRepository.GetProspectByContactNoAsync(contactNos);
                        var oldProspect = duplicateDatasToUpdate.Adapt<List<ViewProspectDto>>();
                        oldProspects.AddRange(oldProspect);
                        duplicateDatasToUpdate.ToList().ForEach(prospect =>
                        {
                            var data = dto.Prospects?.FirstOrDefault(i => (!string.IsNullOrWhiteSpace(i.ContactNo) && (i.ContactNo.Contains(prospect.ContactNo ?? string.Empty) ||
                                                                i.ContactNo.Contains(prospect.AlternateContactNo ?? string.Empty))) ||
                                                                (!string.IsNullOrWhiteSpace(i.AlternateContactNo) && (i.AlternateContactNo.Contains(prospect.AlternateContactNo ?? string.Empty) ||
                                                                i.AlternateContactNo.Contains(prospect.ContactNo ?? string.Empty))));
                            var updatedProspects = V2MappingMethodForUpdateMissingInfodata(data, prospect, masterItems.Projects ?? new(), masterItems.Properties ?? new());
                            prospectsToUpdate.Add(updatedProspects);
                        });
                    }
                    if (tracker.MigrationType == DataMigrationType.OverideExisitingProspectInformation)
                    {
                        var duplicateDatasToUpdate = await _prospectRepository.GetProspectByContactNoAsync(contactNos);
                        var oldProspect = duplicateDatasToUpdate.Adapt<List<ViewProspectDto>>();
                        oldProspects.AddRange(oldProspect);
                        duplicateDatasToUpdate.ToList().ForEach(prospect =>
                        {
                            var data = dto.Prospects?.FirstOrDefault(i => (!string.IsNullOrWhiteSpace(i.ContactNo) && (i.ContactNo.Contains(prospect.ContactNo ?? string.Empty) ||
                                                                i.ContactNo.Contains(prospect.AlternateContactNo ?? string.Empty))) ||
                                                                (!string.IsNullOrWhiteSpace(i.AlternateContactNo) && (i.AlternateContactNo.Contains(prospect.AlternateContactNo ?? string.Empty) ||
                                                                i.AlternateContactNo.Contains(prospect.ContactNo ?? string.Empty))));
                            var updatedProspects = V2MapAllObjects(data, prospect, masterItems.Projects ?? new(), masterItems.Properties ?? new());
                            prospectsToUpdate.Add(updatedProspects);
                        });
                    }

                    var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");
                    var prospectDetails = await UpdateProspectInDapperAsync(prospectsToUpdate, connection, dto.TenantInfoDto?.Id ?? string.Empty, dto.CurrentUserId);
                    var addresses = ExtractAddressFromProspects(prospectsToUpdate);
                    if (addresses?.Any() ?? false)
                    {
                        await V3AddBulkLocations(addresses, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                    }
                    List<ProspectHistory> prospectHistories = new();
                    var prospectVM = prospectsToUpdate.Adapt<List<Lrb.Application.DataManagement.Web.Dtos.ViewProspectDto>>();
                    Lrb.Application.Identity.Users.UserDetailsDto? user = await _userService.GetAsync(dto.CurrentUserId.ToString(), CancellationToken.None);
                    List<ProspectHistory> allHistories = new();
                    foreach (var prospectDto in prospectVM)
                    {
                        var oldProspect = oldProspects?.FirstOrDefault(p => p.Id == prospectDto.Id);
                        prospectDto.V2SetUserViewForProspect(dto.HistoryUsers?.Adapt<List<Lrb.Application.Identity.Users.UserDetailsDto>>() ?? new(), currentUserId: dto.CurrentUserId);
                        oldProspect?.V2SetUserViewForProspect(dto.HistoryUsers?.Adapt<List<Lrb.Application.Identity.Users.UserDetailsDto>>() ?? new(), currentUserId: dto.CurrentUserId);
                        var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectDto, oldProspect, user.Id, 1, masterItems.ProspectStatuses ?? new(), masterItems.PropetyTypes ?? new(), masterItems.ProspectSources ?? new(), _userService, dto.CancellationToken);
                        allHistories.AddRange(histories);
                    }
                    await CreateProspectHistoryInDapperAsync(allHistories, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                    if (tracker != null)
                    {
                        tracker.ProspectUpdatedCount += dto.Prospects.Count;
                        tracker.LastModifiedBy = dto.CurrentUserId;
                        tracker.CreatedBy = dto.CurrentUserId;
                        await _dataMigrateTrackerRepo.UpdateAsync(tracker);
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsDataAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                tracker.Status = UploadStatus.Failed;
                tracker.Message = e?.InnerException?.Message ?? e?.Message;
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _dataMigrateTrackerRepo.UpdateAsync(tracker);
            }
        }
        public Prospect V2MappingMethodForUpdateMissingInfodata(Prospect src, Prospect dest, List<Project> projects, List<Property> properties)
        {
            if (!dest.Enquiries.Any())
            {
                dest.Enquiries = new List<ProspectEnquiry>();
            }
            try
            {
                if (src.Projects?.Any() ?? false)
                {
                    dest.Projects = new List<Project>() { projects.FirstOrDefault(i => i.Name == src.Projects?.FirstOrDefault()?.Name) ?? new() };
                }
                if (src.Properties?.Any() ?? false)
                {
                    dest.Properties = new List<Property>() { properties.FirstOrDefault(i => i.Title == src.Properties?.FirstOrDefault()?.Title) ?? new() };
                }
                if (dest.Email == null || dest.Email == string.Empty)
                { dest.Email = string.IsNullOrEmpty(src.Email) ? dest.Email : src.Email; }
                if (dest.Notes == null) { dest.Notes = string.IsNullOrEmpty(src.Notes) ? dest.Notes : src.Notes; }
                if (string.IsNullOrEmpty(dest.AlternateContactNo))
                {
                    dest.AlternateContactNo = string.IsNullOrEmpty(src.AlternateContactNo) ? dest.AlternateContactNo : src.AlternateContactNo;
                }
                if (dest.AssignTo == null || dest.AssignTo == Guid.Empty)
                {
                    dest.AssignTo = (src.AssignTo != default && src.AssignTo != null) ? src.AssignTo : dest.AssignTo;
                }
                if (dest.AssignedFrom == null || dest.AssignedFrom == Guid.Empty)
                {
                    dest.AssignedFrom = (src.AssignedFrom != default && src.AssignedFrom != null) ? src.AssignedFrom : dest.AssignedFrom;
                }
                //if (dest.AgencyName == null) { dest.AgencyName = string.IsNullOrEmpty(src.AgencyName) ? dest.AgencyName : src.AgencyName; }
                if (dest.Agencies == null || dest.Agencies.Count == 0) { dest.Agencies = src.Agencies ?? dest.Agencies; }
                if (dest.ChannelPartners == null || dest.ChannelPartners.Count == 0) { dest.ChannelPartners = src.ChannelPartners ?? dest.ChannelPartners; }
                if (dest.CreatedBy == null) { dest.CreatedBy = src.CreatedBy; }
                if (dest.ScheduleDate == null) { dest.ScheduleDate = src.ScheduleDate; }
                if (dest.CreatedOn == null) { dest.CreatedOn = src.CreatedOn; }
                if (string.IsNullOrEmpty(dest.Notes)) { dest.Notes = src.Notes; }
                if (dest.Status == null) { dest.Status = src.Status; }
                if (dest.LastModifiedBy == null) { dest.LastModifiedBy = src.LastModifiedBy; }
                dest.UploadTypeName = string.IsNullOrEmpty(src.UploadTypeName) ? dest.UploadTypeName : src.UploadTypeName;
                if (dest.Enquiries.FirstOrDefault().PropertyType == null)
                {
                    dest.Enquiries.FirstOrDefault().PropertyType = src.Enquiries?.FirstOrDefault()?.PropertyType != default ? src.Enquiries?.FirstOrDefault()?.PropertyType : dest.Enquiries[0].PropertyType;
                }
                if (dest.Enquiries.FirstOrDefault().PropertyTypes == null)
                {
                    dest.Enquiries.FirstOrDefault().PropertyTypes = src.Enquiries?.FirstOrDefault()?.PropertyTypes != default ? src.Enquiries?.FirstOrDefault()?.PropertyTypes : dest.Enquiries[0].PropertyTypes;
                }
                if (dest.Enquiries.FirstOrDefault().BHKs == null || dest.Enquiries.FirstOrDefault().BHKs.Count == 0)
                {
                    dest.Enquiries.FirstOrDefault().BHKs = src.Enquiries?.FirstOrDefault()?.BHKs != default ? src.Enquiries?.FirstOrDefault()?.BHKs : dest.Enquiries[0].BHKs;
                }
                // dest.Enquiries.FirstOrDefault().EnquiredFor = src.Enquiries?.FirstOrDefault()?.EnquiredFor != default ? src.Enquiries.FirstOrDefault().EnquiredFor : dest.Enquiries[0].EnquiredFor;
                if (dest.Enquiries.FirstOrDefault().EnquiryTypes == null || dest.Enquiries.FirstOrDefault().EnquiryTypes.Count == 0)
                {
                    dest.Enquiries.FirstOrDefault().EnquiryTypes = src.Enquiries?.FirstOrDefault()?.EnquiryTypes != default ? src.Enquiries?.FirstOrDefault()?.EnquiryTypes : dest.Enquiries[0].EnquiryTypes;
                }
                // if (dest.Enquiries.FirstOrDefault().SaleType == null) { dest.Enquiries.FirstOrDefault().SaleType = dest.Enquiries[0].SaleType; }
                if (string.IsNullOrEmpty(dest.Enquiries.FirstOrDefault().SubSource))
                {
                    dest.Enquiries.FirstOrDefault().SubSource = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.SubSource) ? dest.Enquiries[0].SubSource : src.Enquiries?.FirstOrDefault()?.SubSource;
                }
                if (dest.Enquiries.FirstOrDefault().Source == null)
                {
                    dest.Enquiries.FirstOrDefault().Source = src.Enquiries?.FirstOrDefault()?.Source != default ? src.Enquiries.FirstOrDefault().Source : dest.Enquiries[0].Source;
                }
                if (dest.Enquiries.FirstOrDefault().LowerBudget == null || dest.Enquiries.FirstOrDefault().LowerBudget == 0)
                {
                    dest.Enquiries.FirstOrDefault().LowerBudget = (src.Enquiries?.FirstOrDefault()?.LowerBudget != default && src.Enquiries?.FirstOrDefault()?.LowerBudget != null) ? src.Enquiries?.FirstOrDefault()?.LowerBudget : dest.Enquiries[0].LowerBudget;
                }
                if (dest.Enquiries.FirstOrDefault().UpperBudget == null || dest.Enquiries.FirstOrDefault().UpperBudget == 0)
                {
                    dest.Enquiries.FirstOrDefault().UpperBudget = (src.Enquiries?.FirstOrDefault()?.UpperBudget != default && src.Enquiries?.FirstOrDefault()?.UpperBudget != null) ? src.Enquiries?.FirstOrDefault()?.UpperBudget : dest.Enquiries[0].UpperBudget;
                }
                if (dest.Enquiries.FirstOrDefault().BHKTypes == null || dest.Enquiries.FirstOrDefault().BHKTypes.Count == 0)
                {
                    dest.Enquiries.FirstOrDefault().BHKTypes = src.Enquiries?.FirstOrDefault()?.BHKTypes != default ? src.Enquiries?.FirstOrDefault()?.BHKTypes : dest.Enquiries[0].BHKTypes;
                }
                if (string.IsNullOrEmpty(dest.Enquiries.FirstOrDefault().Currency))
                {
                    dest.Enquiries.FirstOrDefault().Currency = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Currency) ? dest.Enquiries[0].Currency : src.Enquiries?.FirstOrDefault()?.Currency;
                }
                var address = dest.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault();
                if (address == null)
                {
                    address = new Address();
                    address.Id = Guid.Empty;
                    dest.Enquiries.FirstOrDefault()?.Addresses?.Add(address);
                }
                if (string.IsNullOrEmpty(address?.SubLocality))
                {
                    address.SubLocality = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality)
                        ? address.SubLocality
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality;
                }
                if (string.IsNullOrEmpty(address?.City))
                {
                    address.City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City)
                        ? address.City
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City;
                }

                if (string.IsNullOrEmpty(address?.State))
                {
                    address.State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State)
                        ? address.State
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State;
                }
                if (string.IsNullOrEmpty(address?.Country))
                {
                    address.Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country)
                        ? address.Country
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country;
                }
                try
                {
                    if (dest.Enquiries.FirstOrDefault().CarpetArea == null || dest.Enquiries.FirstOrDefault().CarpetArea == 0)
                    {
                        dest.Enquiries.FirstOrDefault().CarpetArea = (src.Enquiries?.FirstOrDefault()?.CarpetArea != default && src.Enquiries?.FirstOrDefault()?.CarpetArea != null) ? src.Enquiries?.FirstOrDefault()?.CarpetArea : dest.Enquiries[0].CarpetArea;
                    }
                    if (dest.Enquiries.FirstOrDefault().CarpetAreaUnitId == Guid.Empty || dest.Enquiries.FirstOrDefault().CarpetAreaUnitId == default)
                    {
                        dest.Enquiries.FirstOrDefault().CarpetAreaUnitId = src.Enquiries.FirstOrDefault().CarpetAreaUnitId != default ? src.Enquiries.FirstOrDefault().CarpetAreaUnitId : dest.Enquiries[0].CarpetAreaUnitId;
                    }
                    if (string.IsNullOrEmpty(address?.SubCommunity))
                    {
                        address.SubCommunity = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubCommunity)
                            ? address.SubCommunity
                            : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubCommunity;
                    }
                    if (string.IsNullOrEmpty(address?.Community))
                    {
                        address.Community = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Community)
                            ? address.Community
                            : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Community;
                    }
                    if (string.IsNullOrEmpty(address?.TowerName))
                    {
                        address.TowerName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.TowerName)
                            ? address.TowerName
                            : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.TowerName;
                    }
                    if (string.IsNullOrEmpty(address?.PostalCode))
                    {
                        address.PostalCode = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.PostalCode)
                            ? address.PostalCode
                            : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.PostalCode;
                    }
                    dest.ChannelPartnerExecutiveName = string.IsNullOrEmpty(src.ChannelPartnerExecutiveName) ? dest.ChannelPartnerExecutiveName : src.ChannelPartnerExecutiveName;
                    dest.ChannelPartnerContactNo = string.IsNullOrEmpty(src.ChannelPartnerContactNo) ? dest.ChannelPartnerContactNo : src.ChannelPartnerContactNo;
                    dest.Designation = string.IsNullOrEmpty(src.Designation) ? dest.Designation : src.Designation;
                    if (dest.Campaigns == null || dest.Campaigns.Count == 0) { dest.Campaigns = src.Campaigns ?? dest.Campaigns; }
                    if (dest.ReferralContactNo == null || dest.ReferralContactNo == string.Empty) { dest.ReferralContactNo = string.IsNullOrEmpty(src.ReferralContactNo) ? dest.ReferralContactNo : src.ReferralContactNo; }
                    if (dest.ReferralName == null || dest.ReferralName == string.Empty) { dest.ReferralName = string.IsNullOrEmpty(src.ReferralName) ? dest.ReferralName : src.ReferralName; }
                    if (dest.ReferralEmail == null || dest.ReferralEmail == string.Empty)
                    {
                        dest.ReferralEmail = string.IsNullOrEmpty(src.ReferralEmail) ? dest.ReferralEmail : src.ReferralEmail;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.Floors == null || dest?.Enquiries?.FirstOrDefault()?.Floors.Count == 0)
                    {
                        dest.Enquiries.FirstOrDefault().Floors = src.Enquiries?.FirstOrDefault()?.Floors != default ? src.Enquiries?.FirstOrDefault()?.Floors : dest.Enquiries[0].Floors;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.Beds == null || dest?.Enquiries?.FirstOrDefault()?.Beds.Count == 0)
                    {
                        dest.Enquiries.FirstOrDefault().Beds = src.Enquiries?.FirstOrDefault()?.Beds != default ? src.Enquiries?.FirstOrDefault()?.Beds : dest.Enquiries[0].Beds;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.Baths == null || dest?.Enquiries?.FirstOrDefault()?.Baths.Count == 0)
                    {
                        dest.Enquiries.FirstOrDefault().Baths = src.Enquiries?.FirstOrDefault()?.Baths != default ? src.Enquiries?.FirstOrDefault()?.Baths : dest.Enquiries[0].Baths;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.OfferType == null || dest?.Enquiries?.FirstOrDefault()?.OfferType == OfferType.None)
                    {
                        dest.Enquiries.FirstOrDefault().OfferType = src.Enquiries?.FirstOrDefault()?.OfferType != default ? src.Enquiries?.FirstOrDefault()?.OfferType : dest.Enquiries[0].OfferType;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.Purpose == null || dest?.Enquiries?.FirstOrDefault()?.Purpose == Purpose.None)
                    {
                        dest.Enquiries.FirstOrDefault().Purpose = src.Enquiries?.FirstOrDefault()?.Purpose != default ? src.Enquiries?.FirstOrDefault()?.Purpose : dest.Enquiries[0].Purpose;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.Furnished == null || dest?.Enquiries?.FirstOrDefault()?.Furnished == FurnishStatus.Unknown)
                    {
                        dest.Enquiries.FirstOrDefault().Furnished = src.Enquiries?.FirstOrDefault()?.Furnished != default ? src.Enquiries?.FirstOrDefault()?.Furnished : dest.Enquiries[0].Furnished;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.NetArea == null || dest?.Enquiries?.FirstOrDefault()?.NetArea == 0)
                    {
                        dest.Enquiries.FirstOrDefault().NetArea = (src.Enquiries?.FirstOrDefault()?.NetArea != default && src.Enquiries?.FirstOrDefault()?.NetArea != null) ? src.Enquiries?.FirstOrDefault()?.NetArea : dest.Enquiries[0].NetArea;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.NetAreaUnitId == Guid.Empty || dest?.Enquiries?.FirstOrDefault()?.NetAreaUnitId == default)
                    {
                        dest.Enquiries.FirstOrDefault().NetAreaUnitId = src.Enquiries.FirstOrDefault().NetAreaUnitId != default ? src.Enquiries.FirstOrDefault().NetAreaUnitId : dest.Enquiries[0].NetAreaUnitId;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.BuiltUpArea == null || dest?.Enquiries?.FirstOrDefault()?.BuiltUpArea == 0)
                    {
                        dest.Enquiries.FirstOrDefault().BuiltUpArea = (src.Enquiries?.FirstOrDefault()?.BuiltUpArea != default && src.Enquiries?.FirstOrDefault()?.BuiltUpArea != null) ? src.Enquiries?.FirstOrDefault()?.BuiltUpArea : dest.Enquiries[0].BuiltUpArea;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.BuiltUpAreaUnitId == Guid.Empty || dest?.Enquiries?.FirstOrDefault()?.BuiltUpAreaUnitId == default)
                    {
                        dest.Enquiries.FirstOrDefault().BuiltUpAreaUnitId = src.Enquiries.FirstOrDefault().BuiltUpAreaUnitId != default ? src.Enquiries.FirstOrDefault().BuiltUpAreaUnitId : dest.Enquiries[0].BuiltUpAreaUnitId;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.PropertyArea == null || dest.Enquiries.FirstOrDefault().PropertyArea == 0)
                    {
                        dest.Enquiries.FirstOrDefault().PropertyArea = (src.Enquiries?.FirstOrDefault()?.PropertyArea != default && src.Enquiries?.FirstOrDefault()?.PropertyArea != null) ? src.Enquiries?.FirstOrDefault()?.PropertyArea : dest.Enquiries[0].PropertyArea;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.PropertyAreaUnitId == Guid.Empty || dest?.Enquiries?.FirstOrDefault()?.PropertyAreaUnitId == default)
                    {
                        dest.Enquiries.FirstOrDefault().PropertyAreaUnitId = src.Enquiries.FirstOrDefault().PropertyAreaUnitId != default ? src.Enquiries.FirstOrDefault().PropertyAreaUnitId : dest.Enquiries[0].PropertyAreaUnitId;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.SaleableArea == null || dest?.Enquiries?.FirstOrDefault()?.SaleableArea == 0)
                    {
                        dest.Enquiries.FirstOrDefault().SaleableArea = (src.Enquiries?.FirstOrDefault()?.SaleableArea != default && src.Enquiries?.FirstOrDefault()?.SaleableArea != null) ? src.Enquiries?.FirstOrDefault()?.SaleableArea : dest.Enquiries[0].SaleableArea;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.SaleableAreaUnitId == Guid.Empty || dest?.Enquiries?.FirstOrDefault()?.SaleableAreaUnitId == default)
                    {
                        dest.Enquiries.FirstOrDefault().SaleableAreaUnitId = src.Enquiries.FirstOrDefault().SaleableAreaUnitId != default ? src.Enquiries.FirstOrDefault().SaleableAreaUnitId : dest.Enquiries[0].SaleableAreaUnitId;
                    }
                    if (string.IsNullOrEmpty(dest?.Enquiries?.FirstOrDefault()?.UnitName))
                    {
                        dest.Enquiries.FirstOrDefault().UnitName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.UnitName) ? dest.Enquiries[0].UnitName : src.Enquiries?.FirstOrDefault()?.UnitName;
                    }
                    if (string.IsNullOrEmpty(dest?.Enquiries?.FirstOrDefault()?.ClusterName))
                    {
                        dest.Enquiries.FirstOrDefault().ClusterName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.ClusterName) ? dest.Enquiries[0].ClusterName : src.Enquiries?.FirstOrDefault()?.ClusterName;
                    }
                    if (dest.Nationality == null || dest.Nationality == string.Empty) 
                    { 
                        dest.Nationality = string.IsNullOrEmpty(src.Nationality) ? dest.Nationality : src.Nationality;
                    }
                    var customeraddress = dest.Address;
                    if (customeraddress == null)
                    {
                        customeraddress = new Address();
                        customeraddress.Id = Guid.Empty;
                    }
                    if (string.IsNullOrEmpty(customeraddress?.SubLocality))
                    {
                        customeraddress.SubLocality = string.IsNullOrEmpty(src?.Address?.SubLocality) ? customeraddress.SubLocality : src.Address?.SubLocality;
                    }
                    if (string.IsNullOrEmpty(address?.City))
                    {
                        customeraddress.City = string.IsNullOrEmpty(src?.Address?.City) ? customeraddress.City : src.Address?.City;
                    }
                    if (string.IsNullOrEmpty(customeraddress?.State))
                    {
                        customeraddress.State = string.IsNullOrEmpty(src?.Address?.State) ? customeraddress.State : src.Address?.State;

                    }
                    if (string.IsNullOrEmpty(customeraddress?.Country))
                    {
                        customeraddress.Country = string.IsNullOrEmpty(src?.Address?.Country) ? customeraddress.Country : src.Address?.Country;

                    }
                    if (string.IsNullOrEmpty(customeraddress?.SubCommunity))
                    {
                        customeraddress.SubCommunity = string.IsNullOrEmpty(src?.Address?.SubCommunity) ? customeraddress.SubCommunity : src.Address?.SubCommunity;

                    }
                    if (string.IsNullOrEmpty(customeraddress?.Community))
                    {
                        customeraddress.Community = string.IsNullOrEmpty(src?.Address?.Community) ? customeraddress.Community : src.Address?.Community;

                    }
                    if (string.IsNullOrEmpty(customeraddress?.TowerName))
                    {
                        customeraddress.TowerName = string.IsNullOrEmpty(src?.Address?.TowerName) ? customeraddress.TowerName : src.Address?.TowerName;

                    }
                    if (string.IsNullOrEmpty(customeraddress?.PostalCode))
                    {
                        customeraddress.PostalCode = string.IsNullOrEmpty(src?.Address?.PostalCode) ? customeraddress.PostalCode : src.Address?.PostalCode;

                    }
                    if (dest.ClosingManager == null || dest.ClosingManager == Guid.Empty) { dest.ClosingManager = (src.ClosingManager != default && src.ClosingManager != null) ? src.ClosingManager : dest.ClosingManager; }
                    if (dest.SourcingManager == null || dest.SourcingManager == Guid.Empty) { dest.SourcingManager = (src.SourcingManager != default && src.SourcingManager != null) ? src.SourcingManager : dest.SourcingManager; }
                    if (dest?.Profession == null)
                    {
                        dest.Profession = src.Profession != default ? src.Profession : dest.Profession;
                    }
                    if (dest.CompanyName == null || dest.CompanyName == string.Empty)
                    {
                        dest.CompanyName = string.IsNullOrEmpty(src.CompanyName) ? dest.CompanyName : src.CompanyName;
                    }
                    if (dest?.Enquiries?.FirstOrDefault()?.PossesionType == null || dest?.Enquiries?.FirstOrDefault()?.PossesionType == PossesionType.None)
                    {
                        dest.Enquiries.FirstOrDefault().PossesionType = src.Enquiries?.FirstOrDefault()?.PossesionType != default ? src.Enquiries?.FirstOrDefault()?.PossesionType : dest.Enquiries[0].PossesionType;
                    }
                    dest.LandLine = string.IsNullOrEmpty(src.LandLine) ? dest.LandLine : src.LandLine;

                    if (dest?.Gender == null || dest.Gender == Gender.NotMentioned)
                    {
                        dest.Gender = src.Gender != default ? src.Gender : dest.Gender;
                    }
                    if (dest?.MaritalStatus == null || dest.MaritalStatus == MaritalStatusType.NotMentioned)
                    {
                        dest.MaritalStatus = src.MaritalStatus != default ? src.MaritalStatus : dest.MaritalStatus;
                    }
                    if (dest.DateOfBirth == null)
                    {
                        dest.DateOfBirth = src.DateOfBirth;
                    }   
                }
                catch
                {

                }
                return dest;
            }
            catch
            {
                return src;
            }
        }
        private Prospect V2MapAllObjects(Prospect src, Prospect dest, List<Project> projects, List<Property> properties)
        {
            if (!dest.Enquiries.Any())
            {
                dest.Enquiries = new List<ProspectEnquiry>();
            }
            try
            {
                if (src.Projects?.Any() ?? false)
                {
                    dest.Projects = new List<Project>() { projects.FirstOrDefault(i => i.Name == src.Projects?.FirstOrDefault()?.Name) ?? new() };
                }
                if (src.Properties?.Any() ?? false)
                {
                    dest.Properties = new List<Property>() { properties.FirstOrDefault(i => i.Title == src.Properties?.FirstOrDefault()?.Title) ?? new() };
                }
                dest.Email = string.IsNullOrEmpty(src.Email) ? dest.Email : src.Email;
                dest.Notes = string.IsNullOrEmpty(src.Notes) ? dest.Notes : src.Notes;
                dest.AlternateContactNo = string.IsNullOrEmpty(src.AlternateContactNo) ? dest.AlternateContactNo : src.AlternateContactNo;
                dest.AssignTo = (src.AssignTo != default && src.AssignTo != null) ? src.AssignTo : dest.AssignTo;
                //dest.AssignedFrom = (src.AssignedFrom != default && src.AssignedFrom != null) ? src.AssignedFrom : dest.AssignedFrom;
                //dest.AgencyName = string.IsNullOrEmpty(src.AgencyName) ? dest.AgencyName : src.AgencyName;
                dest.Agencies = (src.Agencies == null || src.Agencies.Count == 0) ? dest.Agencies : src.Agencies;
                dest.ChannelPartners = (src.ChannelPartners == null || src.ChannelPartners.Count == 0) ? dest.ChannelPartners : src.ChannelPartners;
                dest.CreatedBy = (src.CreatedBy == null || src.CreatedBy == Guid.Empty) ? dest.CreatedBy : src.CreatedBy;
                // dest.CustomFlags = (src.CustomFlags == null || src.CustomFlags.Count == 0) ? dest.CustomFlags : src.CustomFlags;
                dest.LastModifiedBy = src.LastModifiedBy;
                dest.CreatedOn = (src.CreatedOn == null) ? dest.CreatedOn : src.CreatedOn;
                // dest.CreatedBy = src.CreatedBy;
                dest.Notes = (string.IsNullOrEmpty(src.Notes)) ? dest.Notes : src.Notes;
                dest.ScheduleDate = (src.ScheduleDate == null) ? dest.ScheduleDate : src.ScheduleDate;
                dest.Status = src.Status ?? dest.Status;
                dest.UploadTypeName = string.IsNullOrEmpty(src.UploadTypeName) ? dest.UploadTypeName : src.UploadTypeName;
                dest.Enquiries.FirstOrDefault().BHKs = src.Enquiries?.FirstOrDefault()?.BHKs != default ? src.Enquiries?.FirstOrDefault()?.BHKs : dest.Enquiries[0].BHKs;
                dest.Enquiries.FirstOrDefault().EnquiryTypes = src.Enquiries?.FirstOrDefault()?.EnquiryTypes != default ? src.Enquiries?.FirstOrDefault()?.EnquiryTypes : dest.Enquiries[0].EnquiryTypes;
                //dest.Enquiries.FirstOrDefault().SaleType = dest.Enquiries[0].SaleType;
                dest.Enquiries.FirstOrDefault().SubSource = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.SubSource) ? dest.Enquiries[0].SubSource : src.Enquiries?.FirstOrDefault()?.SubSource;
                dest.Enquiries.FirstOrDefault().Currency = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Currency) ? dest.Enquiries[0].Currency : src.Enquiries?.FirstOrDefault()?.Currency;
                dest.Enquiries.FirstOrDefault().Source = src.Enquiries?.FirstOrDefault().Source != default ? src.Enquiries.FirstOrDefault().Source : dest.Enquiries[0].Source;
                dest.Enquiries.FirstOrDefault().LowerBudget = (src.Enquiries?.FirstOrDefault()?.LowerBudget != default && src.Enquiries?.FirstOrDefault()?.LowerBudget != null) ? src.Enquiries?.FirstOrDefault()?.LowerBudget : dest.Enquiries[0].LowerBudget;
                dest.Enquiries.FirstOrDefault().UpperBudget = (src.Enquiries?.FirstOrDefault()?.UpperBudget != default && src.Enquiries?.FirstOrDefault()?.UpperBudget != null) ? src.Enquiries?.FirstOrDefault()?.UpperBudget : dest.Enquiries[0].UpperBudget;
                dest.Enquiries.FirstOrDefault().BHKTypes = src.Enquiries?.FirstOrDefault()?.BHKTypes != default ? src.Enquiries?.FirstOrDefault()?.BHKTypes : dest.Enquiries[0].BHKTypes;
                dest.Enquiries.FirstOrDefault().PropertyType = src.Enquiries?.FirstOrDefault()?.PropertyType != default ? src.Enquiries?.FirstOrDefault()?.PropertyType : dest.Enquiries[0].PropertyType;
                dest.Campaigns = (src.Campaigns == null || src.Campaigns.Count == 0) ? dest.Campaigns : src.Campaigns;
                try
                {
                    dest.Enquiries.FirstOrDefault().Beds = src.Enquiries?.FirstOrDefault()?.Beds != default ? src.Enquiries?.FirstOrDefault()?.Beds : dest.Enquiries[0].Beds;
                    dest.Enquiries.FirstOrDefault().Floors = src.Enquiries?.FirstOrDefault()?.Floors != default ? src.Enquiries?.FirstOrDefault()?.Floors : dest.Enquiries[0].Floors;
                    dest.Enquiries.FirstOrDefault().Baths = src.Enquiries?.FirstOrDefault()?.Baths != default ? src.Enquiries?.FirstOrDefault()?.Baths : dest.Enquiries[0].Baths;
                    dest.Enquiries.FirstOrDefault().OfferType = src.Enquiries?.FirstOrDefault()?.OfferType != default ? src.Enquiries?.FirstOrDefault()?.OfferType : dest.Enquiries[0]?.OfferType;
                    dest.Enquiries.FirstOrDefault().Furnished = src.Enquiries?.FirstOrDefault()?.Furnished != default ? src.Enquiries?.FirstOrDefault()?.Furnished : dest.Enquiries[0]?.Furnished;
                    dest.ReferralContactNo = string.IsNullOrEmpty(src.ReferralContactNo) ? dest.ReferralContactNo : src.ReferralContactNo;
                    dest.ReferralName = string.IsNullOrEmpty(src.ReferralName) ? dest.ReferralName : src.ReferralName;
                    dest.ReferralEmail = string.IsNullOrEmpty(src.ReferralEmail) ? dest.ReferralEmail : src.ReferralEmail;
                    dest.ChannelPartnerExecutiveName = string.IsNullOrEmpty(src.ChannelPartnerExecutiveName) ? dest.ChannelPartnerExecutiveName : src.ChannelPartnerExecutiveName;
                    dest.ChannelPartnerContactNo = string.IsNullOrEmpty(src.ChannelPartnerContactNo) ? dest.ChannelPartnerContactNo : src.ChannelPartnerContactNo;
                    dest.Enquiries.FirstOrDefault().CarpetArea = (src.Enquiries?.FirstOrDefault()?.CarpetArea != default && src.Enquiries?.FirstOrDefault()?.CarpetArea != null) ? src.Enquiries?.FirstOrDefault()?.CarpetArea : dest.Enquiries[0].CarpetArea;
                    dest.Enquiries.FirstOrDefault().CarpetAreaUnitId = (src.Enquiries?.FirstOrDefault()?.CarpetAreaUnitId != default) ? src.Enquiries.FirstOrDefault().CarpetAreaUnitId : dest.Enquiries.FirstOrDefault().CarpetAreaUnitId;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.SubLocality : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.City : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.State : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Country : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubCommunity = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubCommunity) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.SubCommunity : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubCommunity;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Community = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Community) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Community : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Community;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().TowerName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.TowerName) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.TowerName : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.TowerName;
                    dest.Enquiries.FirstOrDefault().NetArea = (src.Enquiries?.FirstOrDefault()?.NetArea != default && src.Enquiries?.FirstOrDefault()?.NetArea != null) ? src.Enquiries?.FirstOrDefault()?.NetArea : dest.Enquiries[0].NetArea;
                    dest.Enquiries.FirstOrDefault().NetAreaUnitId = (src.Enquiries?.FirstOrDefault()?.NetAreaUnitId != default) ? src.Enquiries.FirstOrDefault().NetAreaUnitId : dest.Enquiries.FirstOrDefault().NetAreaUnitId;
                    dest.Enquiries.FirstOrDefault().PropertyArea = (src.Enquiries?.FirstOrDefault()?.PropertyArea != default && src.Enquiries?.FirstOrDefault()?.PropertyArea != null) ? src.Enquiries?.FirstOrDefault()?.PropertyArea : dest.Enquiries[0].PropertyArea;
                    dest.Enquiries.FirstOrDefault().PropertyAreaUnitId = (src.Enquiries?.FirstOrDefault()?.PropertyAreaUnitId != default) ? src.Enquiries.FirstOrDefault().PropertyAreaUnitId : dest.Enquiries.FirstOrDefault().PropertyAreaUnitId;
                    dest.Enquiries.FirstOrDefault().BuiltUpArea = (src.Enquiries?.FirstOrDefault()?.BuiltUpArea != default && src.Enquiries?.FirstOrDefault()?.BuiltUpArea != null) ? src.Enquiries?.FirstOrDefault()?.BuiltUpArea : dest.Enquiries[0].BuiltUpArea;
                    dest.Enquiries.FirstOrDefault().BuiltUpAreaUnitId = (src.Enquiries?.FirstOrDefault()?.BuiltUpAreaUnitId != default) ? src.Enquiries.FirstOrDefault().BuiltUpAreaUnitId : dest.Enquiries.FirstOrDefault().BuiltUpAreaUnitId;
                    dest.Enquiries.FirstOrDefault().SaleableArea = (src.Enquiries?.FirstOrDefault()?.SaleableArea != default && src.Enquiries?.FirstOrDefault()?.SaleableArea != null) ? src.Enquiries?.FirstOrDefault()?.SaleableArea : dest.Enquiries[0].SaleableArea;
                    dest.Enquiries.FirstOrDefault().SaleableAreaUnitId = (src.Enquiries?.FirstOrDefault()?.SaleableAreaUnitId != default) ? src.Enquiries.FirstOrDefault().SaleableAreaUnitId : dest.Enquiries.FirstOrDefault().SaleableAreaUnitId;
                    dest.Enquiries.FirstOrDefault().UnitName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.UnitName) ? dest.Enquiries[0].UnitName : src.Enquiries?.FirstOrDefault()?.UnitName;
                    dest.Enquiries.FirstOrDefault().ClusterName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.ClusterName) ? dest.Enquiries[0].ClusterName : src.Enquiries?.FirstOrDefault()?.ClusterName;
                    dest.Nationality = string.IsNullOrEmpty(src.Nationality) ? dest.Nationality : src.Nationality;
                    dest.Enquiries.FirstOrDefault().PropertyTypes = src.Enquiries?.FirstOrDefault()?.PropertyTypes != default ? src.Enquiries?.FirstOrDefault()?.PropertyTypes : dest.Enquiries[0].PropertyTypes;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.SubLocality : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.City : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.State : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Country : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country;
                    dest.Enquiries.FirstOrDefault().Purpose = src.Enquiries?.FirstOrDefault()?.Purpose != default ? src.Enquiries?.FirstOrDefault()?.Purpose : dest.Enquiries[0]?.Purpose;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().PostalCode = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.PostalCode) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.PostalCode : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.PostalCode;
                    dest.Address.SubLocality = string.IsNullOrEmpty(src.Address?.SubLocality) ? dest.Address?.SubLocality : src.Address?.SubLocality;
                    dest.Address.City = string.IsNullOrEmpty(src.Address?.City) ? dest.Address?.City : src.Address?.City;
                    dest.Address.State = string.IsNullOrEmpty(src.Address?.State) ? dest.Address?.State : src.Address?.State;
                    dest.Address.Country = string.IsNullOrEmpty(src.Address?.Country) ? dest.Address?.Country : src.Address?.Country;
                    dest.Address.SubCommunity = string.IsNullOrEmpty(src.Address?.SubCommunity) ? dest.Address?.SubCommunity : src.Address?.SubCommunity;
                    dest.Address.Community = string.IsNullOrEmpty(src.Address?.Community) ? dest.Address?.SubLocality : src.Address?.Community;
                    dest.Address.TowerName = string.IsNullOrEmpty(src.Address?.TowerName) ? dest.Address?.TowerName : src.Address?.TowerName;
                    dest.Address.PostalCode = string.IsNullOrEmpty(src.Address?.PostalCode) ? dest.Address?.PostalCode : src.Address?.PostalCode;
                    dest.ClosingManager = (src.ClosingManager != default && src.ClosingManager != null) ? src.ClosingManager : dest.ClosingManager;
                    dest.SourcingManager = (src.SourcingManager != default && src.SourcingManager != null) ? src.SourcingManager : dest.SourcingManager;
                    dest.Profession = src.Profession != default ? src.Profession : dest.Profession;
                    dest.CompanyName = string.IsNullOrEmpty(src.CompanyName) ? dest.CompanyName : src.CompanyName;
                    dest.Enquiries.FirstOrDefault().PossesionType = src.Enquiries?.FirstOrDefault()?.PossesionType != default ? src.Enquiries?.FirstOrDefault()?.PossesionType : dest.Enquiries[0]?.PossesionType;
                    dest.LandLine = string.IsNullOrEmpty(src.LandLine) ? dest.LandLine : src.LandLine;

                    dest.Gender = src.Gender != default ? src.Gender : dest.Gender;
                    dest.MaritalStatus = src.MaritalStatus != default ? src.MaritalStatus : dest.MaritalStatus;
                    dest.DateOfBirth = (src.DateOfBirth == null) ? dest.DateOfBirth : src.DateOfBirth;
                }
                catch
                {

                }
                return dest;
            }
            catch
            {
                return src;
            }
        }
        public async Task<List<Prospect>> UpdateProspectInDapperAsync(List<Prospect> prospects, string connectionString, string tenantId, Guid currentUserId)
        {

            if (!string.IsNullOrEmpty(connectionString))
            {

                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    //leads
                    var addProspects = prospects.Select(i =>
                    {
                        var prospectDto = i.Adapt<ProspectDapperDto>();
                        prospectDto.StatusId = i.Status?.Id ?? Guid.Empty;
                        return prospectDto;
                    }).ToList();

                    var properties = GetMappedProperties<ProspectDapperDto>();
                    var insertQuery = QueryGenerator.V2GenerateUpdateQuery(tenantId, DataBaseDetails.LRBSchema, "Prospects", properties, addProspects);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    //enquiries
                    var enquiries = prospects
                       .Where(prospect => prospect.Enquiries != null && prospect.Enquiries.Any())
                       .SelectMany(prospect =>
                            prospect.Enquiries.Select(enquiry =>
                            {
                                var prospectEnquiryDto = enquiry.Adapt<ProspectEnquiryDapperDto>();
                                prospectEnquiryDto.ProspectId = prospect.Id;
                                prospectEnquiryDto.PropertyTypeId = enquiry.PropertyType?.Id ?? null;
                                prospectEnquiryDto.SourceId = enquiry.Source?.Id ?? Guid.Empty;
                                return prospectEnquiryDto;
                            })
                     ).ToList();
                    if (enquiries.Any())
                    {
                        var enquiryProperties = GetMappedProperties<ProspectEnquiryDapperDto>();
                        var enquiryInsertQuery = QueryGenerator.V2GenerateUpdateQuery(null, DataBaseDetails.LRBSchema, "ProspectEnquiries", enquiryProperties, enquiries);
                        await conn.ExecuteAsync(enquiryInsertQuery, commandTimeout: 1000);
                    }
                    var EnquiryPropTypes = prospects.SelectMany(p => p.Enquiries)
                        .Where(en => en.PropertyTypes != null && en.PropertyTypes.Any())
                        .SelectMany(en => en.PropertyTypes.Select(p => new ProspectEnquiryPropertyTypesDTO
                        {
                            ProspectEnquiriesId = en.Id,
                            PropertyTypesId = p.Id,

                        }));
                    if (EnquiryPropTypes.Any())
                    {
                        List<string> columnNames = new List<string> { "PropertyTypesId", "ProspectEnquiriesId" };
                        var propInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "MasterPropertyTypeProspectEnquiry", columnNames, "PropertyTypesId", "ProspectEnquiriesId", EnquiryPropTypes);
                        await conn.ExecuteAsync(propInsertQuery, commandTimeout: 1000);
                    }


                    //addresses
                    var addresses = prospects.Select(i => i.Enquiries.FirstOrDefault())
                        .Where(i => i.Addresses != null && i.Addresses.Any()).SelectMany(address => address.Addresses).Where(address => address.Id != Guid.Empty).Adapt<List<Lrb.Application.Lead.Web.Dtos.AddressDapperDto>>();

                    if (addresses.Any())
                    {
                        var addressProperties = GetMappedProperties<Lrb.Application.Lead.Web.Dtos.AddressDapperDto>();
                        var addressInsertQuery = QueryGenerator.V2GenerateUpdateQuery(null, DataBaseDetails.LRBSchema, "Addresses", addressProperties, addresses);
                        await conn.ExecuteAsync(addressInsertQuery, commandTimeout: 1000);
                    }

                    var newAddresses = prospects.Select(i => i.Enquiries.FirstOrDefault())
                        .Where(i => i.Addresses != null && i.Addresses.Any()).SelectMany(address => address.Addresses).Where(address => address.Id == Guid.Empty).ToList();
                    newAddresses.ForEach(address => address.Id = Guid.NewGuid()); // Assign new GUIDs
                    var addressDtos = newAddresses.Adapt<List<Lrb.Application.Lead.Web.Dtos.AddressDapperDto>>();

                    if (addressDtos.Any())
                    {
                        var addressProperties = GetMappedProperties<Lrb.Application.Lead.Web.Dtos.AddressDapperDto>();
                        var addressInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "Addresses", addressProperties, addressDtos);
                        await conn.ExecuteAsync(addressInsertQuery, commandTimeout: 1000);
                    }

                    // enquiry address
                    var enquiryAddresses = prospects.Select(i => i.Enquiries.FirstOrDefault())
                    .Where(i => i.Addresses != null && i.Addresses.Any())
                    .SelectMany(en =>
                        en.Addresses.Select(address => new AddressProspectEnquiryDto
                        {
                            ProspectEnquiriesId = en.Id,
                            AddressesId = address.Id
                        })
                    ).ToList();
                    if (enquiryAddresses.Any())
                    {
                        var enquiryAddressProperties = GetMappedProperties<AddressProspectEnquiryDto>();
                        var enquiryAddressInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "AddressProspectEnquiry", enquiryAddressProperties, "ProspectEnquiriesId", "AddressesId", enquiryAddresses);
                        await conn.ExecuteAsync(enquiryAddressInsertQuery, commandTimeout: 1000);
                    }

                    //projects
                    var prospectProjects = prospects
                    .Where(prospect => prospect.Projects != null && prospect.Projects.Any())
                    .SelectMany(prospect => prospect.Projects.Select(project => new ProjectProspectDto
                    {
                        ProspectsId = prospect.Id,
                        ProjectsId = project.Id
                    })).ToList();
                    if (prospectProjects.Any())
                    {
                        List<string> columnNames = new List<string> { "ProspectsId", "ProjectsId" };
                        var projectInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "ProjectProspect", columnNames, "ProspectsId", "ProjectsId", prospectProjects);
                        await conn.ExecuteAsync(projectInsertQuery, commandTimeout: 1000);
                    }

                    //properties
                    var prospectsProperties = prospects
                    .Where(prospect => prospect.Properties != null && prospect.Properties.Any())
                    .SelectMany(prospect => prospect.Properties.Select(i => new PropertyProspectDto
                    {
                        ProspectsId = prospect.Id,
                        PropertiesId = i.Id
                    })).ToList();
                    if (prospectsProperties.Any())
                    {
                        List<string> columnNames = new List<string> { "ProspectsId", "PropertiesId" };
                        var propertyInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "PropertyProspect", columnNames, "ProspectsId", "PropertiesId", prospectsProperties);
                        await conn.ExecuteAsync(propertyInsertQuery, commandTimeout: 1000);
                    }

                    //agencies
                    var prospectAgencies = prospects
                    .Where(prospect => prospect.Agencies != null && prospect.Agencies.Any())
                    .SelectMany(prospect => prospect.Agencies.Select(i => new AgencyProspectDto
                    {
                        ProspectsId = prospect.Id,
                        AgenciesId = i.Id
                    })).ToList();
                    if (prospectAgencies.Any())
                    {
                        List<string> columnNames = new List<string> { "AgenciesId", "ProspectsId" };
                        var agencyInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "AgencyProspect", columnNames, "ProspectsId", "AgenciesId", prospectAgencies);
                        await conn.ExecuteAsync(agencyInsertQuery, commandTimeout: 1000);
                    }

                    //channel partners
                    var prospectChannelpartners = prospects
                    .Where(prospect => prospect.ChannelPartners != null && prospect.ChannelPartners.Any())
                    .SelectMany(prospect => prospect.ChannelPartners.Select(i => new ChannelPartnerProspectDto
                    {
                        ProspectsId = prospect.Id,
                        ChannelPartnersId = i.Id
                    })).ToList();
                    if (prospectChannelpartners.Any())
                    {
                        List<string> columnNames = new List<string> { "ChannelPartnersId", "ProspectsId" };
                        var channelpartnersInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "ChannelPartnerProspect", columnNames, "ProspectsId", "ChannelPartnersId", prospectChannelpartners);
                        await conn.ExecuteAsync(channelpartnersInsertQuery, commandTimeout: 1000);
                    }
                    //campaigns
                    var prospectCampaigns = prospects
                    .Where(prospect => prospect.Campaigns != null && prospect.Campaigns.Any())
                    .SelectMany(prospect => prospect.Campaigns.Select(i => new CampaignProspectDto
                    {
                        ProspectsId = prospect.Id,
                        CampaignsId = i.Id
                    })).ToList();
                    if (prospectCampaigns.Any())
                    {
                        List<string> columnNames = new List<string> { "CampaignsId", "ProspectsId" };
                        var campaignInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "CampaignProspect", columnNames, prospectCampaigns);
                        await conn.ExecuteAsync(campaignInsertQuery, commandTimeout: 1000);
                    }


                    conn.Close();
                    return prospects.ToList();
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return new();
            }
        }

    }
}
