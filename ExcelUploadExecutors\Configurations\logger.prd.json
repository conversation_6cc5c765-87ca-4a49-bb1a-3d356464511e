﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Hangfire": "Warning",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Serilog": {
    "Using": [
      "AWS.Logger.SeriLog"
    ],
    "LogGroup": "/aws/lightsail/container/prd-lrb-webapi",
    "Region": "ap-south-1",
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId",
      "WithHangfireContext"
    ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Hangfire": "Warning",
        "Microsoft": "Error",
        "Microsoft.Hosting.Lifetime": "Information",
        "System": "Information"
      }
    },
    "Properties": {
      "Application": "Lrb.WebApi.Host"
    },
    "WriteTo": [
      {
        "Args": {
          "path": "Logs/logs.json",
          "formatter": "Serilog.Formatting.Json.JsonFormatter, Serilog",
          "rollingInterval": "Day",
          "restrictedToMinimumLevel": "Information",
          "retainedFileCountLimit": 5
        },
        "Name": "File"
      },
      {
        "Name": "Seq",
        "Args": {
          "serverUrl": "http://localhost:5341"
        }
      },
      {
        "Name": "Hangfire"
      },
      {
        "Name": "Elasticsearch",
        "Args": {
          "nodeUris": "http://localhost:9200;",
          "indexFormat": "Lrb.WebApi.Host-logs-{0:yyyy.MM}",
          "numberOfShards": 2,
          "numberOfReplicas": 1,
          "restrictedToMinimumLevel": "Information"
        }
      }
    ]
  }
}