﻿using Lrb.Application.Common.Exceptions;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Newtonsoft.Json;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint : FBCommonHandler, IFunctionEntryPoint
    {
        public async Task FacebookSyncHandler(InputPayload input)
        {
            Console.WriteLine("FunctionEntryPoint -> FacebookSyncHandler() started");
            var fbAuthResponse = await _facebookAuthResponseRepo.GetByIdAsync(input.TrackerId);
            Console.WriteLine("FunctionEntryPoint -> FacebookSyncHandler(), Facebook Auth Response: "
                + JsonConvert.SerializeObject(fbAuthResponse,
                settings: new() 
                { 
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore, 
                    Formatting = Formatting.Indented 
                }));

            if (fbAuthResponse == null) { throw new NotFoundException("No facebook account found by this id."); }
            var tenantId = input.TenantId;
            if (!string.IsNullOrEmpty(tenantId))
            {
                var tInfo = await _tenantIndependentRepo.GetTenantInfoAsync(tenantId);
                var dto = new FacebookIntegrationDto()
                {
                    FecebookAuthResponseId = fbAuthResponse.Id,
                    AccessToken = fbAuthResponse.LongLivedUserAccessToken,
                    FacebookUserId = fbAuthResponse.FacebookUserId,
                    TenantInfoDto = tInfo,
                    FacebookAccountName = fbAuthResponse.FacebookAccountName,
                    CurrentUserId = input.CurrentUserId,
                };
                await UpdateFacebookAuthResponse(dto);
                Console.WriteLine("FunctionEntryPoint -> FacebookSyncHandler() finished");
            }
        }
    }
}
