﻿// See https://aka.ms/new-console-template for more information
using Lrb.Application.GlobalSettings.Web.Dto;
using Lrb.Infrastructure.Persistence.Configuration.Application;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.Extensions.DependencyInjection;
using Nager.Country.Currencies;
using Newtonsoft.Json;
namespace ExcelUpload;



static class Program
{
    static async Task Main(string[] args)
    {
        int counter = 0;
        while (counter < 3)
        {
            try
            {
                string inputParameter = String.Empty;
                if (args.Length > 0)
                {
                    // get the first command-line argument
                    inputParameter = args[0];   
                    // do something with the argument
                    Console.WriteLine("Received command-line argument: " + inputParameter);
                }
                else
                {
                    Console.WriteLine("No command-line arguments received.");
                }
                Console.WriteLine($"Input received: {inputParameter}");
                var payload = JsonConvert.DeserializeObject<InputPayload>(inputParameter);
                ReportsConfigurationDto? reportconfigation = new();
                if (payload?.reportConfiguration != null)
                {
                    reportconfigation = JsonConvert.DeserializeObject<ReportsConfigurationDto> (payload.reportConfiguration);
                }

                var startup = new Startup();
                IServiceProvider provider = startup.ConfigureServices(payload.TenantId);
                IFunctionEntryPoint _functionEntryPoint = provider.GetRequiredService<IFunctionEntryPoint>();
                ////Task.Factory.StartNew(async () => await _functionEntryPoint.Handler(payload));
                //Task task = Task.Factory.StartNew(async () =>
                //{
                //    await _functionEntryPoint.Handler(payload);
                //});



                //task.Wait();
                //"{\"s3BucketKey\":\"Lead/Leadexcel2.xlsx\",\"mappedColumnsData\":{\"Name\":\"Name\",\"ContactNo\":\"ContactNumber\",\"Email\":\"Email\"}}"





                switch (payload.Type.ToLower())
                {
                    case "leadimport": 
                    case "lead":
                        await _functionEntryPoint.ImportLeadHandlerV2(payload);
                        break;
                    case "leadmigrate":
                        await _functionEntryPoint.MigrateLeadHandlerV2(payload);
                        break;
                    case "property":
                        await _functionEntryPoint.PropertyHandler(payload);
                        break;
                    case "exportleads":
                        await _functionEntryPoint.ExportLeadsHandler(payload);
                        break;
                    case "meetingandvisitreport":
                        await _functionEntryPoint.ExportLeadMeetingAndVisitReportByUserHandler(payload, reportconfigation);
                        break;
                    case "statusreportbyproject":
                        await _functionEntryPoint.ExportLeadStatusReportByProjectHandler(payload, reportconfigation);
                        break;
                    case "statusreportbysource":
                        await _functionEntryPoint.ExportLeadStatusReportBySourceHandler(payload, reportconfigation);
                        break;
                    case "statusreportbyuser":
                        await _functionEntryPoint.ExportLeadStatusReportByUserHandler(payload, reportconfigation);
                        break;
                    case "statusreportbyagency":
                        await _functionEntryPoint.ExportLeadStatusReportByAgencyHandler(payload, reportconfigation);
                        break;
                    case "statusreportbysubsource":
                        await _functionEntryPoint.ExportLeadStatusReportBySubSourceHandler(payload, reportconfigation);
                        break;
                    case "exportleadsbynewfilters":
                        await _functionEntryPoint.ExportLeadsByNewFiltersHandler(payload);
                        break;
                    case "facebooksync":
                        await _functionEntryPoint.FacebookSyncHandler(payload);
                        break;
                    case "fbbulkleadfetch":
                        await _functionEntryPoint.FacebookBulkLeadsFetchHandler(payload);
                        break;
                    case "validateexcel":
                        await _functionEntryPoint.ValidateExcelHandler(payload);
                        break;
                    case "exportfbbulkleads":
                        await _functionEntryPoint.ExportFacebookBulkLeadsHandler(payload);
                        break;
                    case "useractivityreport":
                    case "leadanddatauseractivityreport":
                        await _functionEntryPoint.ExportUserActivityReportHandler(payload, reportconfigation);
                        break;
                    case "googlesheet":
                        await _functionEntryPoint.GoogleSheetHandler(payload);
                        break;
                    case "channelpartner":
                        await _functionEntryPoint.ChannelPartnerHandler(payload);
                        break;
                    case "substatusreportbyuser":
                        await _functionEntryPoint.ExportLeadSubStatusReportByUserHandler(payload, reportconfigation);
                        break;
                    case "substatusreportbysubsource":
                        await _functionEntryPoint.ExportLeadSubStatusReportBySubSourceHandler(payload, reportconfigation);
                        break;
                    case "sendwhatsapptemplateinbulk":
                        await _functionEntryPoint.SendWhatsAppBulkTemplateHandler(payload);
                        break;
                    case "fblogin":
                        await _functionEntryPoint.FacebookLoginHandler(payload);
                        break;
                    case "prospect":
                        await _functionEntryPoint.ProspectHandlerV2(payload);
                        break;
                    case "projectreportbysubstatus":
                        await _functionEntryPoint.ExportLeadProjectReportBySubStatusHandler(payload, reportconfigation);
                        break;
                    case "exportattendance":
                        await _functionEntryPoint.ExportAttendanceHandler(payload);
                        break;
                    case "exportproperties":
                        await _functionEntryPoint.ExportPropertiesHandler(payload);
                        break;
                    case "exportprospects":
                        await _functionEntryPoint.ExportProspectsHandler(payload);
                        break;
                    case "calllogreportbyuser":
                        await _functionEntryPoint.ExportCallLogReportsHandler(payload, reportconfigation);
                        break;
                    case "datastatusreportbyuser":
                        await _functionEntryPoint.ExportDataStatusReportByUserHandler(payload, reportconfigation);
                        break;
                    case "datasubsourcereportbystatus":
                        await _functionEntryPoint.ExportDataStatusReportBySubSourceHandler(payload, reportconfigation);
                        break;
                    case "datasourcereportbystatus":
                        await _functionEntryPoint.ExportDataStatusReportBySourceHandler(payload, reportconfigation);
                        break;
                    case "dataprojectreportbystatus":
                        await _functionEntryPoint.ExportDataStatusReportByProjectHandler(payload, reportconfigation);
                        break;
                    #region Custom Filter 
                    case "exportleadsbycustomfilters":
                        await _functionEntryPoint.ExportLeadsByCustomFiltersHandler(payload);
                        break;
                    case "lead_statusreportbyagency": 
                        await _functionEntryPoint.ExportAgencyReportByLeadStatusHandler(payload, reportconfigation);
                        break;
                    case "lead_statusreportbyproject": 
                        await _functionEntryPoint.ExportProjectReportByLeadStatusHandler(payload, reportconfigation);
                        break;
                    case "lead_statusreportbysource":
                        await _functionEntryPoint.ExportSourceReportByLeadStatusHandler(payload, reportconfigation);
                        break;
                    case "lead_statusreportbysubsource":  
                        await _functionEntryPoint.ExportSubsourceReportByLeadStatusHandler(payload, reportconfigation);
                        break;
                    case "lead_statusreportbyuser": 
                        await _functionEntryPoint.ExportUserReportByLeadStatusHandler(payload, reportconfigation);
                        break;
                    case "exportprospectsbycustomfilters":
                        await _functionEntryPoint.ExportProspectsByCustomFiltersHandler(payload);
                        break;
                    #endregion
                    case "exportdatacalllogreportbyuser":
                        await _functionEntryPoint.ExportDataCallLogReportsHandler(payload, reportconfigation);
                        break;
                    case "recieveddatebysource":
                        await _functionEntryPoint.ExportLeadDatewiseSourceCountHandler(payload, reportconfigation);
                        break;
                    case "userdataactivityreport":
                        await _functionEntryPoint.ExportDataUserActivityReportHandler(payload, reportconfigation);
                        break;
                    case "uservssource":
                        await _functionEntryPoint.ExportUserVsSourceReportHandler(payload, reportconfigation);
                        break;
                    case "uservssubsource":
                        await _functionEntryPoint.ExportUserVsSubSourceReportHandler(payload, reportconfigation);
                        break;
                    case "exportusers":
                        await _functionEntryPoint.ExportUserReportHandler(payload);
                        break;
                    case "unit":
                        await _functionEntryPoint.UnitHandler(payload);
                        break;
                    case "datamigrate":
                       await _functionEntryPoint.MigrateDataHandlerV2(payload);
                        break;
                    case "userdelete":
                        await _functionEntryPoint.UserDeleteHandler(payload);
                        break;
                    case "marketingagency":
                        await _functionEntryPoint.MarketingAgencyHandler(payload);
                        break;
                    case "exportmarketingagency":
                        await _functionEntryPoint.ExportMarketingAgencyHandler(payload);
                        break;
                    case "exportmarketingchannelpartner":
                        await _functionEntryPoint.ExportMarketingChannelPartnerHandler(payload);
                        break;
                    case "marketingchannelpartner":
                        await _functionEntryPoint.MarketingChannelPartnerHandler(payload);
                        break;
                    case "exportteam":
                        await _functionEntryPoint.ExportTeamReportHandler(payload);
                        break;
                    case "userimport":
                        await _functionEntryPoint.BulkUserImportHandler(payload);
                        break;
                    case "listingaddress":
                        await _functionEntryPoint.ListingSourceAddressHandler(payload);
                        break;
                    case "exportpropertieslisting":
                        await _functionEntryPoint.ExportPropertiesListingHandler(payload);
                        break;
                    case "customaddress":
                        await _functionEntryPoint.ImportCustomAddressFromExcelHandler(payload);
                        break;
                    case "exportprojects":
                        await _functionEntryPoint.ExportProjectsHandler(payload);
                        break;
                    case "project":
                        await _functionEntryPoint.ProjectHandler(payload);
                        break;
                    case "marketingcamapign":
                        await _functionEntryPoint.MarketingCampaignHandler(payload);
                        break;
                    case "exportmarketingcampaign":
                        await _functionEntryPoint.ExportMarketingCampaignHandler(payload);
                        break;
                    case "refrenceid":
                        await _functionEntryPoint.BulkImportReferenceIdHandler(payload);
                        break;
                    case "googleadslogin":
                        await _functionEntryPoint.GoogleAdsLoginHandler(payload);
                        break;
                    case "googleadsbulkleadfetch":
                        await _functionEntryPoint.GoogleAdsBulkLeadsFetchHandler(payload);
                        break;
                }
                break;
            }
            catch (Exception e)
            {
                if ((e.Source?.ToLower().Contains("Npgsql".ToLower()) ?? false) || (e.Message?.ToLower().Contains("An exception has been raised that is likely due to a transient failure".ToLower()) ?? false))
                {
                    counter++;
                }
                else
                {
                    throw;
                } 
            }
        }
    }
}