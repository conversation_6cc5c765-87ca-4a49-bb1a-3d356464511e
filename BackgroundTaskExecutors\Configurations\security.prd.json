﻿{
  "SecuritySettings": {
    "Provider": "Jwt",
    "RequireConfirmedAccount": true,
    "JwtSettings": {
      "key": "S0M3RAN0MS3CR3T!1!MAG1C!1!",
      "tokenExpirationInMinutes": 60,
      "refreshTokenExpirationInDays": 7
    },
    "AzureAd": {
      "Instance": "https://login.microsoftonline.com/",
      "Domain": "<Your Domain>.onmicrosoft.com",
      "TenantId": "organizations",
      "ClientId": "<Your ClientId of the AzureAd Server App Registration>",
      "Scopes": "access_as_user",
      "RootIssuer": "https://sts.windows.net/<Your AzureAd TenantId>/"
    },
    "CognitoJwtSettings": {
      "Region": "ap-south-1",
      "UserPoolId": "ap-south-1_hWGhC3OS9",
      "WebAppClientId": "5cm991n5subnkoed8ipclp486o",
      "WebAppClientSecret": "1q91qkgvkgo3limuk0n98tssifp1hnpfpf8b97pfpujn43kvas3e",
      "MobileAppClientId": "569lrkqe573tg41kc73jesnj61",
      "MicrositeClientId": "",
      "AccessKeyId": "********************",
      "AccessSecretKey": "TnH2dknft7vO0FrXvcvLFe7C8kgDJ8Oa3qIUBu0r",
      "JsonWebKeySet": {
        "keys": [
          {
            "alg": "RS256",
            "e": "AQAB",
            "kid": "aiJxf+P1xUnk1pvkM6s5XKefeU9hVeouQd7X9+vQgYo=",
            "kty": "RSA",
            "n": "n6gIqatRKDR41SC22trOu2KM-0SVsqMRfpYPlKabg5q2tOp65xCblcAfBaVppBWAslzQrIXFiJ3lLNgmTLssDDEtHZlO6XBm_Z8dAn0dSSNydfNktANKXcSEm87rlSizCt8WkQVM3YMnTJDZTG-XPg-JjHvZFNevs5ICa-_NcVetqCGPctbu8I7BHDM8hippuERQLnFhNIOXcKIsEZVzT0tca02acScju9CpTZZRLJR6hBbmWCJ5oam3ojUb2H_bn9BuBSkf69SFIyetSefsCaJPY_o6HzR5KaglGD96ox8NDXx7X8ywSWnZNX_Q_zNdiOy_TYoLsvtRa0D21qROHw",
            "use": "sig"
          },
          {
            "alg": "RS256",
            "e": "AQAB",
            "kid": "eWurL9Z07rPmSdikjM9jmFCvgOyVsN94mV/TsxsCNA0=",
            "kty": "RSA",
            "n": "9UY8QJigGdu_x8I1ro4sTCnyqJzSnUIWOHun9bfnBs9qla5PZiHQGQatbWVI9g7jbusxJ8NMl0LJO0lKw2xQsv-GIc8qk4swVT5k-wc8WWBVVRr000Vnpr_r6Ajw-hos33qDig5RWBmCyuyVRypTqIW9-I4tapYCQ1GiIu04AcMBPK-unoKYJpCsL-XBmgbPQn3aY4XSjlKN9xMlE4Yk1L7M2P6cxfiF8LIBtGp2Jg7kCR063OCZ_J49Kf-Xj0DJlvBozO6obsxu5xuXkyPvKZnRXGbJb1ZFFP5eZa1ZmFZajdmAmqpPmvGKl45hWSjnSIr51r-72oo9DZ3YwRFDQQ",
            "use": "sig"
          }
        ]
      }
    },
    "Swagger": {
      "AuthorizationUrl": "https://login.microsoftonline.com/organizations/oauth2/v2.0/authorize",
      "TokenUrl": "https://login.microsoftonline.com/organizations/oauth2/v2.0/token",
      "ApiScope": "api://<Your ClientId of the AzureAd Server App Registration>/access_as_user",
      "OpenIdClientId": "<Your ClientId of the AzureAd Client App Registration>"
    }
  }
}