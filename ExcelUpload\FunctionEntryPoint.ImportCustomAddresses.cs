﻿using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using System.Data;
using Lrb.Domain.Enums;
using Lrb.Domain.Entities.CustomAddress;
using Microsoft.Graph;
using Lrb.Application.CustomAddress.Web.Address.Mapping;
using Lrb.Application.CustomAddress.Web.Address.Spec;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ImportCustomAddressFromExcelHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _customAddresstrackerRepo.FirstOrDefaultAsync(new BulkCustomAddressesTrackerSpec(input.TrackerId));
            try
            {
                if (tracker != null)
                {
                    tracker.MappedColumnsData = tracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                    tracker.Status = UploadStatus.Started;
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;

                    await _customAddresstrackerRepo.UpdateAsync(tracker);
                    Console.WriteLine($"FunctionEntryPoint() -> CustomAddressUploadTracker Updated Status: {tracker.Status} \n {JsonConvert.SerializeObject(tracker)}");

                    #region Convert to Datatable

                    Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", tracker.S3BucketKey);
                    DataTable dataTable = new();
                    if (tracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                    {
                        using MemoryStream memoryStream = new();
                        fileStream.CopyTo(memoryStream);
                        dataTable = CSVHelper.CSVToDataTable(memoryStream);
                    }
                    else
                    {
                        dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, tracker.SheetName);
                    }

                    List<InvalidProspect> invalids = new();
                    int totalRows = dataTable.Rows.Count;
                    for (int i = totalRows - 1; i >= 0; i--)
                    {
                        var row = dataTable.Rows[i];
                        if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                        {
                            row.Delete();
                        }
                    }
                    if (dataTable.Rows.Count <= 0)
                    {
                        throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                    }
                    totalRows = dataTable.Rows.Count;
                    Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");

                    #endregion

                    #region Fetch All requires data
                  //  var listingSource = await _customListingSource.FirstOrDefaultAsync(new GetAllListingSourceByIds(tracker.ListingSourceId ?? Guid.Empty));
                    #endregion

                    var customAddresses = dataTable.ConvertToCustomAddress(tracker.MappedColumnsData);
                    customAddresses.ForEach(i => i.SetCustomAddress(tracker.MappedColumnsData, input.CurrentUserId));

                    tracker.Status = UploadStatus.InProgress;
                    tracker.TotalCount = totalRows;
                    //tracker.InvalidCount = invalidChannelPartners.Count;
                    await _customAddresstrackerRepo.UpdateAsync(tracker);

                    if (customAddresses.Count > 0)
                    {
                        int customAddressChunks = customAddresses.Count > 5000 ? 5000 : customAddresses.Count;
                        var chunks = customAddresses.Chunk(customAddressChunks).Select(i => new ConcurrentBag<CustomAddressDirectory>(i));
                        var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);
                        var chunkIndex = 1;
                        foreach (var chunk in chunks.ToList())
                        {
                            var backgroundDto = new BulkCustomAddressUploadBackgroundDto()
                            {
                                TrackerId = tracker?.Id ?? Guid.Empty,
                                TenantInfoDto = tenantInfo,
                                CancellationToken = CancellationToken.None,
                                CustomAddresses = new(chunk)
                            };
                            if (chunkIndex == chunks.Count())
                            {
                                backgroundDto.IsLastChunk = true;
                            }
                            await ExecuteDBOperationsAsync(backgroundDto);
                            chunkIndex++;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "ListingSourceAddressHandler -> ListingSourceHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
        }

        public async Task ExecuteDBOperationsAsync(BulkCustomAddressUploadBackgroundDto dto)
        {
            ListingSourceAddress listingAddress = new();
            var tracker = await _customAddresstrackerRepo.FirstOrDefaultAsync(new BulkCustomAddressesTrackerSpec(dto.TrackerId));
            try
            {
                await _customAddressRepo.AddRangeAsync(dto.CustomAddresses);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.CustomAddresses?.Count ?? 0 ;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _customAddresstrackerRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == tracker.InvalidCount)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _customAddresstrackerRepo.UpdateAsync(tracker);
            }
        }
    }
    public class BulkCustomAddressUploadBackgroundDto
    {
        public List<CustomAddressDirectory>? CustomAddresses { get; set; }
        public CancellationToken CancellationToken { get; set; }
        public Lrb.Application.Notifications.Dtos.LrbTenantInfoDto? TenantInfoDto { get; set; }
        public Guid CurrentUserId { get; set; }
        public Guid TrackerId { get; set; }
        public bool IsLastChunk { get; set; }
    }
}
