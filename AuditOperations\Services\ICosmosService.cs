﻿using Microsoft.Azure.Cosmos;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace AuditOperation
{
    public interface ICosmosService
    {
        Task<Database> CreateDatabaseAsync(string databaseId, CancellationToken cancellationToken = default);
        Task<Container> CreateContainerAsync(Database database, string containerId, CancellationToken cancellationToken = default);
        Task<T> AddItemToContainerAsync<T>(T item, Container container, CancellationToken cancellationToken = default);
        Task<IEnumerable<T>> QueryItemsAsync<T>(string query, Container container, CancellationToken cancellationToken = default);
        Task<T> ReplaceItemAsync<T>(T item, Container container, CancellationToken cancellationToken = default);
        Task<bool> DeleteItemAsync<T>(T item, Container container, CancellationToken cancellationToken = default);
        Task AddItemAsync<T>(T item, Container container, CancellationToken cancellationToken = default);
        Task<Container> InitCosmosDbAsync(CancellationToken cancellationToken = default);
    }
}
